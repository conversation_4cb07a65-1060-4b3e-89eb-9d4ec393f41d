<?php
// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/config.php";
require_once "../../config/database.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
session_start();
if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "تقارير الموردين";
$page_icon = "fas fa-chart-bar";
$base_url = "../../";

// معالجة الفلاتر
$date_from = isset($_GET['date_from']) ? clean($conn, $_GET['date_from']) : date('Y-m-01');
$date_to = isset($_GET['date_to']) ? clean($conn, $_GET['date_to']) : date('Y-m-d');
$supplier_id = isset($_GET['supplier_id']) ? intval($_GET['supplier_id']) : 0;

// الحصول على قائمة الموردين للفلتر
$suppliers_query = "SELECT id, name FROM suppliers ORDER BY name";
$suppliers_result = $conn->query($suppliers_query);

// إحصائيات عامة
$stats = [];

// إجمالي الموردين
$total_suppliers_query = "SELECT COUNT(*) as count FROM suppliers";
$result = $conn->query($total_suppliers_query);
$stats['total_suppliers'] = $result->fetch_assoc()['count'];

// الموردين النشطين (لديهم مشتريات في الفترة)
$active_suppliers_query = "SELECT COUNT(DISTINCT supplier_id) as count FROM purchases 
                          WHERE purchase_date BETWEEN ? AND ?";
$stmt = $conn->prepare($active_suppliers_query);
$stmt->bind_param("ss", $date_from, $date_to);
$stmt->execute();
$stats['active_suppliers'] = $stmt->get_result()->fetch_assoc()['count'];

// إجمالي المشتريات في الفترة
$total_purchases_query = "SELECT COUNT(*) as count, SUM(total_amount) as total 
                         FROM purchases WHERE purchase_date BETWEEN ? AND ?";
if ($supplier_id > 0) {
    $total_purchases_query .= " AND supplier_id = ?";
    $stmt = $conn->prepare($total_purchases_query);
    $stmt->bind_param("ssi", $date_from, $date_to, $supplier_id);
} else {
    $stmt = $conn->prepare($total_purchases_query);
    $stmt->bind_param("ss", $date_from, $date_to);
}
$stmt->execute();
$purchase_data = $stmt->get_result()->fetch_assoc();
$stats['total_purchases_count'] = $purchase_data['count'];
$stats['total_purchases_amount'] = $purchase_data['total'] ?? 0;

// أكبر الموردين (حسب قيمة المشتريات)
$top_suppliers_query = "SELECT s.id, s.name, s.company, COUNT(p.id) as purchases_count, 
                       SUM(p.total_amount) as total_amount, s.balance
                       FROM suppliers s 
                       LEFT JOIN purchases p ON s.id = p.supplier_id 
                       AND p.purchase_date BETWEEN ? AND ?
                       GROUP BY s.id, s.name, s.company, s.balance
                       ORDER BY total_amount DESC LIMIT 10";
$stmt = $conn->prepare($top_suppliers_query);
$stmt->bind_param("ss", $date_from, $date_to);
$stmt->execute();
$top_suppliers = $stmt->get_result();

// الموردين حسب الرصيد
$balance_stats_query = "SELECT 
                       SUM(CASE WHEN balance > 0 THEN 1 ELSE 0 END) as positive_count,
                       SUM(CASE WHEN balance < 0 THEN 1 ELSE 0 END) as negative_count,
                       SUM(CASE WHEN balance = 0 THEN 1 ELSE 0 END) as zero_count,
                       SUM(CASE WHEN balance > 0 THEN balance ELSE 0 END) as positive_total,
                       SUM(CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END) as negative_total
                       FROM suppliers";
$result = $conn->query($balance_stats_query);
$balance_stats = $result->fetch_assoc();
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> | نظام Zero لإدارة المحلات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- نمط الخط العربي -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 0;
            margin-bottom: 2rem;
            color: white;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .stats-primary .stats-number { color: #007bff; }
        .stats-success .stats-number { color: #28a745; }
        .stats-warning .stats-number { color: #ffc107; }
        .stats-danger .stats-number { color: #dc3545; }
        
        .report-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 1.5rem;
        }
        
        .card-body {
            padding: 2rem;
        }
        
        .filter-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 2rem 0;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .btn {
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">تقارير وإحصائيات شاملة عن الموردين</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="index.php" class="btn btn-light btn-lg">
                        <i class="fas fa-list me-2"></i>
                        قائمة الموردين
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- فلاتر التقرير -->
        <div class="filter-card">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2"></i>
                فلاتر التقرير
            </h5>
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="<?php echo $date_from; ?>">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="<?php echo $date_to; ?>">
                </div>
                <div class="col-md-4">
                    <label for="supplier_id" class="form-label">المورد</label>
                    <select class="form-select" id="supplier_id" name="supplier_id">
                        <option value="0">جميع الموردين</option>
                        <?php while ($supplier = $suppliers_result->fetch_assoc()): ?>
                            <option value="<?php echo $supplier['id']; ?>" 
                                    <?php echo ($supplier_id == $supplier['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($supplier['name']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i> تطبيق
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- الإحصائيات العامة -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card stats-primary">
                    <div class="stats-number"><?php echo $stats['total_suppliers']; ?></div>
                    <div class="stats-label">إجمالي الموردين</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card stats-success">
                    <div class="stats-number"><?php echo $stats['active_suppliers']; ?></div>
                    <div class="stats-label">الموردين النشطين</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card stats-warning">
                    <div class="stats-number"><?php echo $stats['total_purchases_count']; ?></div>
                    <div class="stats-label">فواتير المشتريات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card stats-danger">
                    <div class="stats-number"><?php echo formatMoney($stats['total_purchases_amount'], false); ?></div>
                    <div class="stats-label">قيمة المشتريات</div>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات الأرصدة -->
        <div class="row">
            <div class="col-md-6">
                <div class="report-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-wallet me-2"></i>
                            إحصائيات الأرصدة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="balanceChart"></canvas>
                        </div>
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="text-success">
                                    <h4><?php echo $balance_stats['positive_count']; ?></h4>
                                    <small>مستحقات لنا</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-danger">
                                    <h4><?php echo $balance_stats['negative_count']; ?></h4>
                                    <small>مستحقات علينا</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">
                                    <h4><?php echo $balance_stats['zero_count']; ?></h4>
                                    <small>رصيد صفر</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="report-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>
                            توزيع المبالغ
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="amountChart"></canvas>
                        </div>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="text-success">
                                    <h5><?php echo formatMoney($balance_stats['positive_total'], false); ?></h5>
                                    <small>إجمالي المستحقات لنا</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-danger">
                                    <h5><?php echo formatMoney($balance_stats['negative_total'], false); ?></h5>
                                    <small>إجمالي المستحقات علينا</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- أكبر الموردين -->
        <div class="report-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    أكبر الموردين (حسب قيمة المشتريات في الفترة المحددة)
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>الترتيب</th>
                                <th>اسم المورد</th>
                                <th>الشركة</th>
                                <th>عدد الفواتير</th>
                                <th>قيمة المشتريات</th>
                                <th>الرصيد الحالي</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $rank = 1;
                            while ($supplier = $top_suppliers->fetch_assoc()): 
                            ?>
                            <tr>
                                <td>
                                    <span class="badge bg-primary"><?php echo $rank; ?></span>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($supplier['name']); ?></strong>
                                </td>
                                <td><?php echo htmlspecialchars($supplier['company'] ?? '-'); ?></td>
                                <td>
                                    <span class="badge bg-info"><?php echo $supplier['purchases_count']; ?></span>
                                </td>
                                <td>
                                    <strong class="text-success">
                                        <?php echo formatMoney($supplier['total_amount'] ?? 0); ?>
                                    </strong>
                                </td>
                                <td>
                                    <?php 
                                    $balance_class = '';
                                    if ($supplier['balance'] > 0) {
                                        $balance_class = 'text-success';
                                    } elseif ($supplier['balance'] < 0) {
                                        $balance_class = 'text-danger';
                                    } else {
                                        $balance_class = 'text-muted';
                                    }
                                    ?>
                                    <span class="<?php echo $balance_class; ?>">
                                        <?php echo formatMoney($supplier['balance']); ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="view.php?id=<?php echo $supplier['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php 
                            $rank++;
                            endwhile; 
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- أزرار التصدير -->
        <div class="text-center mb-4">
            <button onclick="window.print()" class="btn btn-secondary btn-lg me-2">
                <i class="fas fa-print me-2"></i>
                طباعة التقرير
            </button>
            <a href="export.php?<?php echo http_build_query($_GET); ?>" class="btn btn-success btn-lg">
                <i class="fas fa-file-excel me-2"></i>
                تصدير Excel
            </a>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // رسم بياني للأرصدة
        const balanceCtx = document.getElementById('balanceChart').getContext('2d');
        new Chart(balanceCtx, {
            type: 'doughnut',
            data: {
                labels: ['مستحقات لنا', 'مستحقات علينا', 'رصيد صفر'],
                datasets: [{
                    data: [
                        <?php echo $balance_stats['positive_count']; ?>,
                        <?php echo $balance_stats['negative_count']; ?>,
                        <?php echo $balance_stats['zero_count']; ?>
                    ],
                    backgroundColor: ['#28a745', '#dc3545', '#6c757d'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // رسم بياني للمبالغ
        const amountCtx = document.getElementById('amountChart').getContext('2d');
        new Chart(amountCtx, {
            type: 'pie',
            data: {
                labels: ['مستحقات لنا', 'مستحقات علينا'],
                datasets: [{
                    data: [
                        <?php echo $balance_stats['positive_total']; ?>,
                        <?php echo $balance_stats['negative_total']; ?>
                    ],
                    backgroundColor: ['#28a745', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>

</body>
</html>
