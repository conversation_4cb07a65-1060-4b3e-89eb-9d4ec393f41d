<?php
/**
 * متتبع أرصدة العملاء المفصل
 * Detailed Customer Balance Tracker
 */

// استدعاء ملفات الإعدادات
require_once "config/db_config.php";
require_once "includes/functions.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "متتبع أرصدة العملاء";

// البحث عن عميل محدد
$search_customer = $_GET['search'] ?? '';
$customer_filter = '';
if (!empty($search_customer)) {
    $customer_filter = "WHERE c.name LIKE '%" . $conn->real_escape_string($search_customer) . "%'";
}

// الحصول على العملاء مع تفاصيل أرصدتهم
$customers_query = "
    SELECT c.id, c.name, c.phone, c.balance as old_balance,
           COALESCE(c.wallet_balance, 0) as wallet_balance,
           COALESCE(c.debt_balance, 0) as debt_balance,
           COALESCE(c.credit_limit, 0) as credit_limit,
           c.last_transaction_date,
           (SELECT COUNT(*) FROM sales WHERE customer_id = c.id) as total_sales,
           (SELECT SUM(final_amount) FROM sales WHERE customer_id = c.id) as total_sales_amount,
           (SELECT SUM(paid_amount) FROM sales WHERE customer_id = c.id) as total_paid_amount,
           (SELECT SUM(final_amount - paid_amount) FROM sales WHERE customer_id = c.id AND final_amount > paid_amount) as calculated_debt
    FROM customers c 
    $customer_filter
    ORDER BY c.name
";

$customers_result = $conn->query($customers_query);

// إحصائيات عامة
$stats_query = "
    SELECT 
        COUNT(*) as total_customers,
        SUM(CASE WHEN balance > 0 THEN 1 ELSE 0 END) as customers_with_old_balance,
        SUM(CASE WHEN COALESCE(debt_balance, 0) > 0 THEN 1 ELSE 0 END) as customers_with_debt,
        SUM(CASE WHEN COALESCE(wallet_balance, 0) > 0 THEN 1 ELSE 0 END) as customers_with_wallet,
        SUM(balance) as total_old_balance,
        SUM(COALESCE(debt_balance, 0)) as total_debt_balance,
        SUM(COALESCE(wallet_balance, 0)) as total_wallet_balance
    FROM customers
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .customer-row {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .customer-row:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .customer-row.has-debt {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .customer-row.has-wallet {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .customer-row.has-both {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .balance-badge {
            font-size: 0.9em;
            padding: 4px 8px;
            border-radius: 12px;
        }
        .search-box {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-chart-line me-3"></i>
                متتبع أرصدة العملاء
            </h1>
            <p class="lead">مراجعة مفصلة لجميع أرصدة العملاء والديون</p>
        </div>

        <!-- البحث -->
        <div class="search-box">
            <form method="GET" class="row align-items-center">
                <div class="col-md-8">
                    <input type="text" name="search" class="form-control form-control-lg" 
                           placeholder="ابحث عن عميل بالاسم..." 
                           value="<?php echo htmlspecialchars($search_customer); ?>">
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary btn-lg w-100">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
                <div class="col-md-2">
                    <a href="?" class="btn btn-secondary btn-lg w-100">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                </div>
            </form>
        </div>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="stat-card">
                    <h3 class="text-primary"><?php echo $stats['total_customers']; ?></h3>
                    <small>إجمالي العملاء</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <h3 class="text-warning"><?php echo $stats['customers_with_old_balance']; ?></h3>
                    <small>رصيد قديم</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <h3 class="text-danger"><?php echo $stats['customers_with_debt']; ?></h3>
                    <small>لهم ديون</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <h3 class="text-success"><?php echo $stats['customers_with_wallet']; ?></h3>
                    <small>رصيد محفوظ</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <h3 class="text-info"><?php echo number_format($stats['total_debt_balance'], 0); ?></h3>
                    <small>إجمالي الديون</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <h3 class="text-success"><?php echo number_format($stats['total_wallet_balance'], 0); ?></h3>
                    <small>إجمالي المحفوظ</small>
                </div>
            </div>
        </div>

        <!-- قائمة العملاء -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-users me-2"></i>تفاصيل العملاء</h3>
            </div>
            <div class="card-body">
                
                <?php if ($customers_result->num_rows > 0): ?>
                    
                    <?php while ($customer = $customers_result->fetch_assoc()): ?>
                        <?php
                        $has_debt = $customer['debt_balance'] > 0;
                        $has_wallet = $customer['wallet_balance'] > 0;
                        $has_old_balance = $customer['old_balance'] != 0;
                        
                        $row_class = 'customer-row';
                        if ($has_debt && $has_wallet) {
                            $row_class .= ' has-both';
                        } elseif ($has_debt) {
                            $row_class .= ' has-debt';
                        } elseif ($has_wallet) {
                            $row_class .= ' has-wallet';
                        }
                        
                        $calculated_debt = $customer['calculated_debt'] ?? 0;
                        $debt_difference = $calculated_debt - $customer['debt_balance'];
                        ?>
                        
                        <div class="<?php echo $row_class; ?>">
                            <div class="row align-items-center">
                                
                                <!-- اسم العميل -->
                                <div class="col-md-3">
                                    <h6 class="mb-1">
                                        <i class="fas fa-user me-2"></i>
                                        <?php echo htmlspecialchars($customer['name']); ?>
                                    </h6>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($customer['phone']); ?>
                                    </small>
                                </div>
                                
                                <!-- الأرصدة -->
                                <div class="col-md-4">
                                    <div class="d-flex gap-2 flex-wrap">
                                        <?php if ($customer['old_balance'] != 0): ?>
                                            <span class="badge bg-warning balance-badge">
                                                رصيد قديم: <?php echo number_format($customer['old_balance'], 2); ?>
                                            </span>
                                        <?php endif; ?>
                                        
                                        <?php if ($customer['debt_balance'] > 0): ?>
                                            <span class="badge bg-danger balance-badge">
                                                دين: <?php echo number_format($customer['debt_balance'], 2); ?>
                                            </span>
                                        <?php endif; ?>
                                        
                                        <?php if ($customer['wallet_balance'] > 0): ?>
                                            <span class="badge bg-success balance-badge">
                                                محفوظ: <?php echo number_format($customer['wallet_balance'], 2); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- إحصائيات المبيعات -->
                                <div class="col-md-3">
                                    <small class="d-block">
                                        <strong>المبيعات:</strong> <?php echo $customer['total_sales']; ?> فاتورة
                                    </small>
                                    <small class="d-block">
                                        <strong>إجمالي المبيعات:</strong> <?php echo number_format($customer['total_sales_amount'] ?? 0, 2); ?>
                                    </small>
                                    <small class="d-block">
                                        <strong>إجمالي المدفوع:</strong> <?php echo number_format($customer['total_paid_amount'] ?? 0, 2); ?>
                                    </small>
                                </div>
                                
                                <!-- حالة الدين -->
                                <div class="col-md-2">
                                    <?php if ($debt_difference > 0.01): ?>
                                        <div class="alert alert-warning alert-sm p-2 mb-1">
                                            <small>
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                دين غير مسجل: <?php echo number_format($debt_difference, 2); ?>
                                            </small>
                                        </div>
                                    <?php elseif ($debt_difference < -0.01): ?>
                                        <div class="alert alert-info alert-sm p-2 mb-1">
                                            <small>
                                                <i class="fas fa-info-circle me-1"></i>
                                                دين زائد: <?php echo number_format(abs($debt_difference), 2); ?>
                                            </small>
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-success alert-sm p-2 mb-1">
                                            <small>
                                                <i class="fas fa-check me-1"></i>
                                                متطابق
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="d-flex gap-1">
                                        <a href="pages/customers/view.php?id=<?php echo $customer['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="pages/sales/index.php?customer_id=<?php echo $customer['id']; ?>" 
                                           class="btn btn-sm btn-outline-info" title="مبيعات العميل">
                                            <i class="fas fa-receipt"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    <?php endwhile; ?>
                    
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php if (!empty($search_customer)): ?>
                            لم يتم العثور على عملاء يطابقون البحث: "<?php echo htmlspecialchars($search_customer); ?>"
                        <?php else: ?>
                            لا توجد عملاء في النظام
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- أدوات الإدارة -->
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h3><i class="fas fa-tools me-2"></i>أدوات الإدارة</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="fix-customer-balances.php" class="btn btn-warning btn-lg w-100 mb-2">
                            <i class="fas fa-magic me-2"></i>
                            إصلاح الأرصدة
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="pages/customers/index.php" class="btn btn-primary btn-lg w-100 mb-2">
                            <i class="fas fa-users me-2"></i>
                            إدارة العملاء
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="pages/sales/index.php" class="btn btn-info btn-lg w-100 mb-2">
                            <i class="fas fa-receipt me-2"></i>
                            مراجعة المبيعات
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="apply-balance-update-final.php" class="btn btn-success btn-lg w-100 mb-2">
                            <i class="fas fa-database me-2"></i>
                            تحديث النظام
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <h2>📊 متتبع أرصدة العملاء! 📊</h2>
            <p class="lead">مراقبة دقيقة لجميع الأرصدة والديون</p>
            
            <a href="index.php" class="btn btn-light btn-lg mt-3">
                <i class="fas fa-home me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحديث الصفحة كل 30 ثانية
        setTimeout(function() {
            location.reload();
        }, 30000);
        
        // إضافة تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const customerRows = document.querySelectorAll('.customer-row');
            customerRows.forEach((row, index) => {
                setTimeout(() => {
                    row.style.opacity = '0';
                    row.style.transform = 'translateY(20px)';
                    row.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        row.style.opacity = '1';
                        row.style.transform = 'translateY(0)';
                    }, 50);
                }, index * 100);
            });
        });
    </script>

</body>
</html>
