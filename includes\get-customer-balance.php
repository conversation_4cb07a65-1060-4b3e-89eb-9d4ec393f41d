<?php
/**
 * الحصول على رصيد العميل عبر AJAX
 * Get Customer Balance via AJAX
 */

// استدعاء ملفات الإعدادات
require_once "../config/db_config.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION["user_id"])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit();
}

// قراءة البيانات
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['customer_id']) || empty($input['customer_id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف العميل مطلوب']);
    exit();
}

$customer_id = intval($input['customer_id']);

try {
    // التحقق من وجود الأعمدة الجديدة
    $check_columns = $conn->query("SHOW COLUMNS FROM customers LIKE 'wallet_balance'");
    $has_new_columns = $check_columns->num_rows > 0;
    
    if ($has_new_columns) {
        // استخدام النظام الجديد
        $query = "SELECT 
                    COALESCE(wallet_balance, 0) as wallet_balance,
                    COALESCE(debt_balance, 0) as debt_balance,
                    name
                  FROM customers 
                  WHERE id = ?";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $customer_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($customer = $result->fetch_assoc()) {
            echo json_encode([
                'success' => true,
                'wallet_balance' => floatval($customer['wallet_balance']),
                'debt_balance' => floatval($customer['debt_balance']),
                'customer_name' => $customer['name'],
                'system_type' => 'advanced'
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'العميل غير موجود']);
        }
    } else {
        // استخدام النظام القديم
        $query = "SELECT balance, name FROM customers WHERE id = ?";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $customer_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($customer = $result->fetch_assoc()) {
            $balance = floatval($customer['balance']);
            
            echo json_encode([
                'success' => true,
                'wallet_balance' => $balance > 0 ? $balance : 0,
                'debt_balance' => $balance < 0 ? abs($balance) : 0,
                'customer_name' => $customer['name'],
                'system_type' => 'legacy'
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'العميل غير موجود']);
        }
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
?>
