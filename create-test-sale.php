<?php
/**
 * إنشاء مبيعة تجريبية لاختبار النظام
 * Create Test Sale for System Testing
 */

require_once "config/db_config.php";
require_once "includes/functions.php";
require_once "includes/advanced-balance-functions.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "إنشاء مبيعة تجريبية";
$result_message = '';

// معالجة إنشاء المبيعة التجريبية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_test_sale'])) {
    $customer_id = intval($_POST['customer_id']);
    $total_amount = floatval($_POST['total_amount']);
    $paid_amount = floatval($_POST['paid_amount']);
    
    if ($customer_id > 1 && $total_amount > 0) {
        try {
            $conn->begin_transaction();
            
            // توليد رقم المبيعة
            $sale_number = generateSaleNumber($conn);
            $sale_date = date('Y-m-d');
            $final_amount = $total_amount;
            $payment_method = 'cash';
            $notes = 'مبيعة تجريبية لاختبار النظام';
            
            // إدراج المبيعة
            $insert_query = "INSERT INTO sales (sale_number, customer_id, user_id, sale_date, total_amount, discount_amount,
                                              final_amount, paid_amount, payment_method, notes)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $conn->prepare($insert_query);
            $discount_amount = 0;
            $user_id = $_SESSION['user_id'] ?? 1;
            
            $stmt->bind_param("siisdddss", $sale_number, $customer_id, $user_id, $sale_date,
                             $total_amount, $discount_amount, $final_amount, $paid_amount, $payment_method, $notes);
            $stmt->execute();
            
            $sale_id = $conn->insert_id;
            
            // مزامنة تلقائية لرصيد العميل
            autoSyncCustomerBalance($customer_id);
            
            // تحديث مبلغ الدين في المبيعة
            $debt_amount = $final_amount - $paid_amount;
            $update_sale_debt = "UPDATE sales SET debt_amount = ? WHERE id = ?";
            $sale_debt_stmt = $conn->prepare($update_sale_debt);
            $sale_debt_stmt->bind_param("di", $debt_amount, $sale_id);
            $sale_debt_stmt->execute();
            
            $conn->commit();
            
            $result_message = "<div class='alert alert-success'>
                                <i class='fas fa-check-circle me-2'></i>
                                تم إنشاء المبيعة التجريبية بنجاح!<br>
                                <strong>رقم المبيعة:</strong> $sale_number<br>
                                <strong>المبلغ الإجمالي:</strong> " . number_format($total_amount, 2) . " ريال<br>
                                <strong>المدفوع:</strong> " . number_format($paid_amount, 2) . " ريال<br>
                                <strong>المتبقي:</strong> " . number_format($debt_amount, 2) . " ريال
                              </div>";
            
        } catch (Exception $e) {
            $conn->rollback();
            $result_message = "<div class='alert alert-danger'>
                                <i class='fas fa-times-circle me-2'></i>
                                خطأ في إنشاء المبيعة: " . $e->getMessage() . "
                              </div>";
        }
    } else {
        $result_message = "<div class='alert alert-warning'>
                            <i class='fas fa-exclamation-triangle me-2'></i>
                            يرجى اختيار عميل صحيح وإدخال مبلغ أكبر من صفر
                          </div>";
    }
}

// الحصول على قائمة العملاء
$customers_query = "SELECT id, name, phone FROM customers WHERE id > 1 ORDER BY name";
$customers_result = $conn->query($customers_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header bg-primary text-white text-center">
                        <h2><i class="fas fa-plus-circle me-2"></i><?php echo $page_title; ?></h2>
                        <p class="mb-0">لاختبار المزامنة التلقائية للأرصدة</p>
                    </div>
                    <div class="card-body">
                        
                        <?php echo $result_message; ?>
                        
                        <!-- نموذج إنشاء المبيعة -->
                        <form method="POST">
                            <div class="mb-3">
                                <label for="customer_id" class="form-label">العميل</label>
                                <select class="form-select" id="customer_id" name="customer_id" required>
                                    <option value="">-- اختر العميل --</option>
                                    <?php while ($customer = $customers_result->fetch_assoc()): ?>
                                        <option value="<?php echo $customer['id']; ?>">
                                            <?php echo htmlspecialchars($customer['name']); ?>
                                            <?php if ($customer['phone']): ?>
                                                (<?php echo htmlspecialchars($customer['phone']); ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="total_amount" class="form-label">المبلغ الإجمالي</label>
                                <input type="number" class="form-control" id="total_amount" name="total_amount" 
                                       step="0.01" min="0.01" value="100.00" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="paid_amount" class="form-label">المبلغ المدفوع</label>
                                <input type="number" class="form-control" id="paid_amount" name="paid_amount" 
                                       step="0.01" min="0" value="50.00" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">المبلغ المتبقي (دين)</label>
                                <input type="text" class="form-control" id="remaining_amount" readonly>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" name="create_test_sale" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>إنشاء مبيعة تجريبية
                                </button>
                            </div>
                        </form>
                        
                        <hr>
                        
                        <!-- روابط مفيدة -->
                        <div class="text-center">
                            <h6>روابط مفيدة:</h6>
                            <div class="btn-group-vertical w-100" role="group">
                                <a href="pages/customers/advanced-balance.php" class="btn btn-outline-primary">
                                    <i class="fas fa-users me-2"></i>إدارة أرصدة العملاء
                                </a>
                                <a href="debug-customer-balance.php" class="btn btn-outline-warning">
                                    <i class="fas fa-bug me-2"></i>تشخيص النظام
                                </a>
                                <a href="sync-customer-balances.php" class="btn btn-outline-info">
                                    <i class="fas fa-sync me-2"></i>مزامنة الأرصدة
                                </a>
                                <a href="balance-management-center.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-chart-line me-2"></i>مركز إدارة الأرصدة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // حساب المبلغ المتبقي تلقائياً
        function calculateRemaining() {
            const total = parseFloat(document.getElementById('total_amount').value) || 0;
            const paid = parseFloat(document.getElementById('paid_amount').value) || 0;
            const remaining = total - paid;
            
            document.getElementById('remaining_amount').value = remaining.toFixed(2) + ' ريال';
            
            // تغيير لون الحقل حسب القيمة
            const remainingField = document.getElementById('remaining_amount');
            if (remaining > 0) {
                remainingField.className = 'form-control text-danger fw-bold';
            } else if (remaining < 0) {
                remainingField.className = 'form-control text-warning fw-bold';
            } else {
                remainingField.className = 'form-control text-success fw-bold';
            }
        }
        
        // ربط الأحداث
        document.getElementById('total_amount').addEventListener('input', calculateRemaining);
        document.getElementById('paid_amount').addEventListener('input', calculateRemaining);
        
        // حساب أولي
        calculateRemaining();
    </script>
</body>
</html>
