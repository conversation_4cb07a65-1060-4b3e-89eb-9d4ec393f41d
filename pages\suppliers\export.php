<?php
// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/config.php";
require_once "../../config/database.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
session_start();
if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

// معالجة الفلاتر
$date_from = isset($_GET['date_from']) ? clean($conn, $_GET['date_from']) : date('Y-m-01');
$date_to = isset($_GET['date_to']) ? clean($conn, $_GET['date_to']) : date('Y-m-d');
$supplier_id = isset($_GET['supplier_id']) ? intval($_GET['supplier_id']) : 0;
$export_type = isset($_GET['type']) ? clean($conn, $_GET['type']) : 'suppliers';

// تحديد نوع التصدير
switch ($export_type) {
    case 'purchases':
        exportPurchases($conn, $date_from, $date_to, $supplier_id);
        break;
    case 'balances':
        exportBalances($conn);
        break;
    default:
        exportSuppliers($conn, $date_from, $date_to, $supplier_id);
        break;
}

/**
 * تصدير بيانات الموردين
 */
function exportSuppliers($conn, $date_from, $date_to, $supplier_id) {
    // الحصول على بيانات الموردين مع إحصائيات المشتريات
    $query = "SELECT s.*, 
              COUNT(p.id) as purchases_count,
              COALESCE(SUM(p.total_amount), 0) as total_purchases,
              COALESCE(MAX(p.purchase_date), 'لا توجد مشتريات') as last_purchase_date
              FROM suppliers s 
              LEFT JOIN purchases p ON s.id = p.supplier_id 
              AND p.purchase_date BETWEEN ? AND ?";
    
    if ($supplier_id > 0) {
        $query .= " WHERE s.id = ?";
        $stmt = $conn->prepare($query . " GROUP BY s.id ORDER BY s.name");
        $stmt->bind_param("ssi", $date_from, $date_to, $supplier_id);
    } else {
        $stmt = $conn->prepare($query . " GROUP BY s.id ORDER BY s.name");
        $stmt->bind_param("ss", $date_from, $date_to);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    // إعداد headers للتصدير
    $filename = "suppliers_report_" . date('Y-m-d_H-i-s') . ".csv";
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // إنشاء ملف CSV
    $output = fopen('php://output', 'w');
    
    // إضافة BOM للدعم العربي
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // كتابة العناوين
    fputcsv($output, [
        'اسم المورد',
        'اسم الشركة',
        'رقم الهاتف',
        'البريد الإلكتروني',
        'العنوان',
        'الرصيد الحالي',
        'عدد فواتير المشتريات',
        'إجمالي قيمة المشتريات',
        'تاريخ آخر مشتريات',
        'تاريخ التسجيل',
        'ملاحظات'
    ]);
    
    // كتابة البيانات
    while ($row = $result->fetch_assoc()) {
        fputcsv($output, [
            $row['name'],
            $row['company'] ?? '',
            $row['phone'] ?? '',
            $row['email'] ?? '',
            $row['address'] ?? '',
            number_format($row['balance'], 2),
            $row['purchases_count'],
            number_format($row['total_purchases'], 2),
            $row['last_purchase_date'],
            date('Y-m-d', strtotime($row['created_at'])),
            $row['notes'] ?? ''
        ]);
    }
    
    fclose($output);
    exit();
}

/**
 * تصدير بيانات المشتريات
 */
function exportPurchases($conn, $date_from, $date_to, $supplier_id) {
    $query = "SELECT p.*, s.name as supplier_name, s.company, u.name as user_name
              FROM purchases p 
              LEFT JOIN suppliers s ON p.supplier_id = s.id 
              LEFT JOIN users u ON p.user_id = u.id 
              WHERE p.purchase_date BETWEEN ? AND ?";
    
    if ($supplier_id > 0) {
        $query .= " AND p.supplier_id = ?";
        $stmt = $conn->prepare($query . " ORDER BY p.purchase_date DESC");
        $stmt->bind_param("ssi", $date_from, $date_to, $supplier_id);
    } else {
        $stmt = $conn->prepare($query . " ORDER BY p.purchase_date DESC");
        $stmt->bind_param("ss", $date_from, $date_to);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    // إعداد headers للتصدير
    $filename = "purchases_report_" . date('Y-m-d_H-i-s') . ".csv";
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // إنشاء ملف CSV
    $output = fopen('php://output', 'w');
    
    // إضافة BOM للدعم العربي
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // كتابة العناوين
    fputcsv($output, [
        'رقم الفاتورة',
        'اسم المورد',
        'اسم الشركة',
        'تاريخ المشتريات',
        'المبلغ الإجمالي',
        'المبلغ المدفوع',
        'المبلغ المتبقي',
        'المستخدم',
        'ملاحظات'
    ]);
    
    // كتابة البيانات
    while ($row = $result->fetch_assoc()) {
        $remaining = $row['total_amount'] - $row['paid_amount'];
        fputcsv($output, [
            $row['purchase_number'] ?? '',
            $row['supplier_name'] ?? '',
            $row['company'] ?? '',
            $row['purchase_date'],
            number_format($row['total_amount'], 2),
            number_format($row['paid_amount'], 2),
            number_format($remaining, 2),
            $row['user_name'] ?? '',
            $row['notes'] ?? ''
        ]);
    }
    
    fclose($output);
    exit();
}

/**
 * تصدير بيانات الأرصدة
 */
function exportBalances($conn) {
    $query = "SELECT s.*, 
              COUNT(p.id) as total_purchases,
              COALESCE(SUM(p.total_amount), 0) as total_amount,
              COALESCE(MAX(p.purchase_date), 'لا توجد مشتريات') as last_purchase_date
              FROM suppliers s 
              LEFT JOIN purchases p ON s.id = p.supplier_id 
              GROUP BY s.id 
              ORDER BY s.balance DESC";
    
    $result = $conn->query($query);
    
    // إعداد headers للتصدير
    $filename = "suppliers_balances_" . date('Y-m-d_H-i-s') . ".csv";
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // إنشاء ملف CSV
    $output = fopen('php://output', 'w');
    
    // إضافة BOM للدعم العربي
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // كتابة العناوين
    fputcsv($output, [
        'اسم المورد',
        'اسم الشركة',
        'رقم الهاتف',
        'الرصيد الحالي',
        'حالة الرصيد',
        'إجمالي المشتريات',
        'عدد الفواتير',
        'تاريخ آخر مشتريات',
        'تاريخ التسجيل'
    ]);
    
    // كتابة البيانات
    while ($row = $result->fetch_assoc()) {
        $balance_status = '';
        if ($row['balance'] > 0) {
            $balance_status = 'مستحقات لنا';
        } elseif ($row['balance'] < 0) {
            $balance_status = 'مستحقات علينا';
        } else {
            $balance_status = 'رصيد صفر';
        }
        
        fputcsv($output, [
            $row['name'],
            $row['company'] ?? '',
            $row['phone'] ?? '',
            number_format($row['balance'], 2),
            $balance_status,
            number_format($row['total_amount'], 2),
            $row['total_purchases'],
            $row['last_purchase_date'],
            date('Y-m-d', strtotime($row['created_at']))
        ]);
    }
    
    fclose($output);
    exit();
}
?>
