<?php
// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/config.php";
require_once "../../config/database.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
session_start();
if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "تفاصيل المورد";
$page_icon = "fas fa-truck";
$base_url = "../../";

// التحقق من وجود معرف المورد
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error_message'] = "معرف المورد غير صحيح";
    header("Location: index.php");
    exit();
}

$supplier_id = intval($_GET['id']);

// الحصول على بيانات المورد
$query = "SELECT * FROM suppliers WHERE id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $supplier_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    $_SESSION['error_message'] = "المورد غير موجود";
    header("Location: index.php");
    exit();
}

$supplier = $result->fetch_assoc();

// الحصول على إحصائيات المورد
$stats = [];

// إجمالي المشتريات
$purchases_query = "SELECT COUNT(*) as count, SUM(total_amount) as total FROM purchases WHERE supplier_id = ?";
$stmt = $conn->prepare($purchases_query);
$stmt->bind_param("i", $supplier_id);
$stmt->execute();
$purchases_result = $stmt->get_result();
if ($purchases_result->num_rows > 0) {
    $purchases_data = $purchases_result->fetch_assoc();
    $stats['purchases_count'] = $purchases_data['count'] ?? 0;
    $stats['purchases_total'] = $purchases_data['total'] ?? 0;
} else {
    $stats['purchases_count'] = 0;
    $stats['purchases_total'] = 0;
}

// آخر المشتريات
$recent_purchases_query = "SELECT p.*, u.name as user_name 
                          FROM purchases p 
                          LEFT JOIN users u ON p.user_id = u.id 
                          WHERE p.supplier_id = ? 
                          ORDER BY p.id DESC LIMIT 5";
$stmt = $conn->prepare($recent_purchases_query);
$stmt->bind_param("i", $supplier_id);
$stmt->execute();
$recent_purchases = $stmt->get_result();

// التحقق من وجود عمود company
$columns_query = "SHOW COLUMNS FROM suppliers LIKE 'company'";
$has_company_column = $conn->query($columns_query)->num_rows > 0;
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> | نظام Zero لإدارة المحلات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- نمط الخط العربي -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 0;
            margin-bottom: 2rem;
            color: white;
        }
        
        .info-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .card-body {
            padding: 2rem;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        
        .info-value {
            color: #212529;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .balance-positive {
            color: #28a745;
            font-weight: bold;
        }
        
        .balance-negative {
            color: #dc3545;
            font-weight: bold;
        }
        
        .balance-zero {
            color: #6c757d;
            font-weight: bold;
        }
        
        .btn {
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">عرض تفاصيل المورد: <?php echo htmlspecialchars($supplier['name']); ?></p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="edit.php?id=<?php echo $supplier['id']; ?>" class="btn btn-warning btn-lg">
                        <i class="fas fa-edit me-2"></i>
                        تعديل البيانات
                    </a>
                    <a href="index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-list me-2"></i>
                        قائمة الموردين
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- عرض الرسائل -->
        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- بيانات المورد -->
            <div class="col-md-8">
                <div class="info-card">
                    <div class="card-header">
                        <h3 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            بيانات المورد
                        </h3>
                    </div>
                    
                    <div class="card-body">
                        <div class="info-item">
                            <span class="info-label">
                                <i class="fas fa-user me-2"></i>
                                اسم المورد:
                            </span>
                            <span class="info-value"><?php echo htmlspecialchars($supplier['name']); ?></span>
                        </div>
                        
                        <?php if ($has_company_column && !empty($supplier['company'])): ?>
                        <div class="info-item">
                            <span class="info-label">
                                <i class="fas fa-building me-2"></i>
                                اسم الشركة:
                            </span>
                            <span class="info-value"><?php echo htmlspecialchars($supplier['company']); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($supplier['phone'])): ?>
                        <div class="info-item">
                            <span class="info-label">
                                <i class="fas fa-phone me-2"></i>
                                رقم الهاتف:
                            </span>
                            <span class="info-value"><?php echo htmlspecialchars($supplier['phone']); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($supplier['email'])): ?>
                        <div class="info-item">
                            <span class="info-label">
                                <i class="fas fa-envelope me-2"></i>
                                البريد الإلكتروني:
                            </span>
                            <span class="info-value"><?php echo htmlspecialchars($supplier['email']); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($supplier['address'])): ?>
                        <div class="info-item">
                            <span class="info-label">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                العنوان:
                            </span>
                            <span class="info-value"><?php echo htmlspecialchars($supplier['address']); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <div class="info-item">
                            <span class="info-label">
                                <i class="fas fa-wallet me-2"></i>
                                الرصيد الحالي:
                            </span>
                            <span class="info-value">
                                <?php 
                                $balance_class = '';
                                if ($supplier['balance'] > 0) {
                                    $balance_class = 'balance-positive';
                                } elseif ($supplier['balance'] < 0) {
                                    $balance_class = 'balance-negative';
                                } else {
                                    $balance_class = 'balance-zero';
                                }
                                ?>
                                <span class="<?php echo $balance_class; ?>">
                                    <?php echo formatMoney($supplier['balance']); ?>
                                </span>
                            </span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">
                                <i class="fas fa-calendar me-2"></i>
                                تاريخ التسجيل:
                            </span>
                            <span class="info-value"><?php echo formatDate($supplier['created_at']); ?></span>
                        </div>
                        
                        <?php if (!empty($supplier['notes'])): ?>
                        <div class="info-item">
                            <span class="info-label">
                                <i class="fas fa-sticky-note me-2"></i>
                                ملاحظات:
                            </span>
                            <span class="info-value"><?php echo nl2br(htmlspecialchars($supplier['notes'])); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- الإحصائيات -->
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-number"><?php echo $stats['purchases_count']; ?></div>
                    <div class="stats-label">إجمالي فواتير المشتريات</div>
                </div>
                
                <div class="stats-card">
                    <div class="stats-number"><?php echo formatMoney($stats['purchases_total'], false); ?></div>
                    <div class="stats-label">إجمالي قيمة المشتريات</div>
                </div>
                
                <div class="text-center">
                    <a href="../purchases/add.php?supplier_id=<?php echo $supplier['id']; ?>" class="btn btn-success btn-lg w-100 mb-2">
                        <i class="fas fa-plus me-2"></i>
                        فاتورة مشتريات جديدة
                    </a>
                    <a href="payments.php?id=<?php echo $supplier['id']; ?>" class="btn btn-warning btn-lg w-100 mb-2">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        إدارة الحساب
                    </a>
                    <a href="../purchases/index.php?supplier_id=<?php echo $supplier['id']; ?>" class="btn btn-info btn-lg w-100">
                        <i class="fas fa-list me-2"></i>
                        عرض جميع المشتريات
                    </a>
                </div>
            </div>
        </div>
        
        <!-- آخر المشتريات -->
        <?php if ($recent_purchases->num_rows > 0): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="table-container">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart me-2"></i>
                            آخر المشتريات
                        </h5>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>المستخدم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($purchase = $recent_purchases->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($purchase['purchase_number'] ?? '-'); ?></td>
                                    <td><?php echo formatDate($purchase['purchase_date']); ?></td>
                                    <td><?php echo formatMoney($purchase['total_amount']); ?></td>
                                    <td><?php echo htmlspecialchars($purchase['user_name'] ?? '-'); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="../purchases/view.php?id=<?php echo $purchase['id']; ?>" 
                                               class="btn btn-sm btn-info text-white" 
                                               data-bs-toggle="tooltip" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="../purchases/print.php?id=<?php echo $purchase['id']; ?>" 
                                               class="btn btn-sm btn-secondary" 
                                               data-bs-toggle="tooltip" title="طباعة">
                                                <i class="fas fa-print"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تفعيل tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    </script>

</body>
</html>
