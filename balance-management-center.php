<?php
/**
 * مركز إدارة الأرصدة الشامل
 * Comprehensive Balance Management Center
 */

// استدعاء ملفات الإعدادات
require_once "config/db_config.php";
require_once "includes/functions.php";
require_once "includes/advanced-balance-functions.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "مركز إدارة الأرصدة";

// فحص حالة النظام
$system_status = [
    'has_advanced_system' => false,
    'customers_stats' => [
        'total_customers' => 0,
        'customers_with_debt' => 0,
        'customers_with_wallet' => 0,
        'total_debt' => 0,
        'total_wallet' => 0
    ],
    'sales_stats' => [
        'total_sales' => 0,
        'total_sales_amount' => 0,
        'total_paid_amount' => 0,
        'total_unpaid_amount' => 0,
        'sales_with_debt' => 0
    ],
    'out_of_sync_count' => 0,
    'recent_transactions' => null
];

try {
    // فحص وجود النظام المتقدم
    $check_advanced = $conn->query("SHOW COLUMNS FROM customers LIKE 'debt_balance'");
    $system_status['has_advanced_system'] = $check_advanced->num_rows > 0;

    // إحصائيات العملاء
    $customers_stats_query = "SELECT
                             COUNT(*) as total_customers,
                             SUM(CASE WHEN COALESCE(debt_balance, balance) > 0 THEN 1 ELSE 0 END) as customers_with_debt,
                             SUM(CASE WHEN COALESCE(wallet_balance, 0) > 0 THEN 1 ELSE 0 END) as customers_with_wallet,
                             SUM(COALESCE(debt_balance, CASE WHEN balance > 0 THEN 0 ELSE ABS(balance) END)) as total_debt,
                             SUM(COALESCE(wallet_balance, CASE WHEN balance > 0 THEN balance ELSE 0 END)) as total_wallet
                             FROM customers WHERE id > 1";
    $customers_stats_result = $conn->query($customers_stats_query);
    if ($customers_stats_result) {
        $customers_stats = $customers_stats_result->fetch_assoc();
        if ($customers_stats) {
            $system_status['customers_stats'] = $customers_stats;
        }
    }

    // إحصائيات المبيعات
    $sales_stats_query = "SELECT
                         COUNT(*) as total_sales,
                         SUM(final_amount) as total_sales_amount,
                         SUM(paid_amount) as total_paid_amount,
                         SUM(final_amount - paid_amount) as total_unpaid_amount,
                         COUNT(CASE WHEN final_amount > paid_amount THEN 1 END) as sales_with_debt
                         FROM sales WHERE customer_id > 1";
    $sales_stats_result = $conn->query($sales_stats_query);
    if ($sales_stats_result) {
        $sales_stats = $sales_stats_result->fetch_assoc();
        if ($sales_stats) {
            $system_status['sales_stats'] = $sales_stats;
        }
    }

    // العملاء الذين يحتاجون مزامنة
    $out_of_sync_query = "SELECT COUNT(*) as count FROM (
                         SELECT c.id
                         FROM customers c
                         LEFT JOIN sales s ON c.id = s.customer_id AND s.final_amount > s.paid_amount
                         WHERE c.id > 1
                         GROUP BY c.id
                         HAVING ABS(COALESCE(c.debt_balance, 0) - COALESCE(SUM(s.final_amount - s.paid_amount), 0)) > 0.01
                         ) as out_of_sync";
    $out_of_sync_result = $conn->query($out_of_sync_query);
    if ($out_of_sync_result) {
        $out_of_sync_data = $out_of_sync_result->fetch_assoc();
        if ($out_of_sync_data) {
            $system_status['out_of_sync_count'] = $out_of_sync_data['count'];
        }
    }

    // أحدث المعاملات
    $recent_transactions_query = "SELECT
                                 s.id, s.sale_number, s.sale_date, s.final_amount, s.paid_amount,
                                 (s.final_amount - s.paid_amount) as debt_amount,
                                 c.name as customer_name
                                 FROM sales s
                                 LEFT JOIN customers c ON s.customer_id = c.id
                                 WHERE s.customer_id > 1 AND s.final_amount > s.paid_amount
                                 ORDER BY s.created_at DESC
                                 LIMIT 10";
    $recent_transactions = $conn->query($recent_transactions_query);
    if ($recent_transactions) {
        $system_status['recent_transactions'] = $recent_transactions;
    }

} catch (Exception $e) {
    $system_status['error'] = "خطأ في فحص النظام: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            height: 100%;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.2);
        }
        .tool-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            height: 100%;
        }
        .tool-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        .status-good { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-danger { background-color: #dc3545; }
        .transaction-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            border-left: 4px solid #007bff;
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-gradient:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-5">
            <h1 class="display-3">
                <i class="fas fa-balance-scale me-3"></i>
                مركز إدارة الأرصدة
            </h1>
            <p class="lead fs-4">إدارة شاملة لأرصدة العملاء والديون</p>
            
            <!-- مؤشر حالة النظام -->
            <div class="mt-3">
                <span class="badge bg-light text-dark fs-6 px-4 py-2">
                    <span class="status-indicator <?php echo $system_status['has_advanced_system'] ? 'status-good' : 'status-warning'; ?>"></span>
                    <?php echo $system_status['has_advanced_system'] ? 'النظام المتقدم مفعل' : 'النظام القديم'; ?>
                </span>
                
                <?php if ($system_status['out_of_sync_count'] > 0): ?>
                    <span class="badge bg-warning text-dark fs-6 px-4 py-2 ms-2">
                        <span class="status-indicator status-warning"></span>
                        <?php echo $system_status['out_of_sync_count']; ?> عميل يحتاج مزامنة
                    </span>
                <?php else: ?>
                    <span class="badge bg-success fs-6 px-4 py-2 ms-2">
                        <span class="status-indicator status-good"></span>
                        جميع الأرصدة متزامنة
                    </span>
                <?php endif; ?>
            </div>
        </div>

        <!-- الإحصائيات الرئيسية -->
        <div class="row mb-5">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="mb-3">
                        <i class="fas fa-users fa-3x text-primary"></i>
                    </div>
                    <h2 class="text-primary"><?php echo number_format($system_status['customers_stats']['total_customers']); ?></h2>
                    <h6 class="text-muted">إجمالي العملاء</h6>
                    <small class="text-success">
                        <?php echo $system_status['customers_stats']['customers_with_debt']; ?> لهم ديون
                    </small>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="mb-3">
                        <i class="fas fa-money-bill-wave fa-3x text-danger"></i>
                    </div>
                    <h2 class="text-danger"><?php echo number_format($system_status['customers_stats']['total_debt'], 0); ?></h2>
                    <h6 class="text-muted">إجمالي الديون</h6>
                    <small class="text-info">
                        من <?php echo $system_status['sales_stats']['sales_with_debt']; ?> مبيعة
                    </small>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="mb-3">
                        <i class="fas fa-piggy-bank fa-3x text-success"></i>
                    </div>
                    <h2 class="text-success"><?php echo number_format($system_status['customers_stats']['total_wallet'], 0); ?></h2>
                    <h6 class="text-muted">الأرصدة المحفوظة</h6>
                    <small class="text-primary">
                        لدى <?php echo $system_status['customers_stats']['customers_with_wallet']; ?> عميل
                    </small>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="mb-3">
                        <i class="fas fa-receipt fa-3x text-info"></i>
                    </div>
                    <h2 class="text-info"><?php echo number_format($system_status['sales_stats']['total_sales']); ?></h2>
                    <h6 class="text-muted">إجمالي المبيعات</h6>
                    <small class="text-warning">
                        بقيمة <?php echo number_format($system_status['sales_stats']['total_sales_amount'], 0); ?> ريال
                    </small>
                </div>
            </div>
        </div>

        <!-- أدوات الإدارة -->
        <div class="row mb-5">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="tool-card">
                    <div class="mb-3">
                        <i class="fas fa-sync-alt fa-3x text-primary"></i>
                    </div>
                    <h5>مزامنة الأرصدة</h5>
                    <p class="text-muted">ربط الأرصدة بين المبيعات وجدول العملاء</p>
                    <a href="sync-customer-balances.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-sync-alt me-2"></i>مزامنة الآن
                    </a>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="tool-card">
                    <div class="mb-3">
                        <i class="fas fa-magic fa-3x text-warning"></i>
                    </div>
                    <h5>إصلاح الأرصدة</h5>
                    <p class="text-muted">تسجيل الديون المفقودة وإصلاح الأخطاء</p>
                    <a href="fix-customer-balances.php" class="btn btn-warning btn-lg">
                        <i class="fas fa-tools me-2"></i>إصلاح الآن
                    </a>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="tool-card">
                    <div class="mb-3">
                        <i class="fas fa-chart-line fa-3x text-info"></i>
                    </div>
                    <h5>متتبع الأرصدة</h5>
                    <p class="text-muted">مراجعة مفصلة لجميع أرصدة العملاء</p>
                    <a href="customer-balance-tracker.php" class="btn btn-info btn-lg">
                        <i class="fas fa-eye me-2"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="tool-card">
                    <div class="mb-3">
                        <i class="fas fa-database fa-3x text-success"></i>
                    </div>
                    <h5>تحديث النظام</h5>
                    <p class="text-muted">تطبيق النظام المتقدم للأرصدة</p>
                    <a href="apply-balance-update-final.php" class="btn btn-success btn-lg">
                        <i class="fas fa-upgrade me-2"></i>تحديث النظام
                    </a>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="tool-card">
                    <div class="mb-3">
                        <i class="fas fa-users fa-3x text-secondary"></i>
                    </div>
                    <h5>إدارة العملاء</h5>
                    <p class="text-muted">مراجعة وإدارة بيانات العملاء</p>
                    <a href="pages/customers/index.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-users me-2"></i>إدارة العملاء
                    </a>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="tool-card">
                    <div class="mb-3">
                        <i class="fas fa-star fa-3x text-gradient"></i>
                    </div>
                    <h5>النظام المتقدم</h5>
                    <p class="text-muted">إنشاء مبيعة جديدة بالنظام المتقدم</p>
                    <a href="pages/sales/add-with-advanced-balance.php" class="btn btn-gradient btn-lg">
                        <i class="fas fa-plus me-2"></i>مبيعة جديدة
                    </a>
                </div>
            </div>
        </div>

        <!-- أحدث المعاملات -->
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h3><i class="fas fa-clock me-2"></i>أحدث المعاملات المعلقة</h3>
            </div>
            <div class="card-body">
                
                <?php if ($system_status['recent_transactions'] && $system_status['recent_transactions']->num_rows > 0): ?>
                    <div class="row">
                        <?php while ($transaction = $system_status['recent_transactions']->fetch_assoc()): ?>
                            <div class="col-md-6 mb-3">
                                <div class="transaction-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">
                                                <i class="fas fa-receipt me-2"></i>
                                                مبيعة رقم: <?php echo htmlspecialchars($transaction['sale_number']); ?>
                                            </h6>
                                            <small class="text-muted">
                                                العميل: <?php echo htmlspecialchars($transaction['customer_name']); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <div class="badge bg-danger">
                                                دين: <?php echo number_format($transaction['debt_amount'], 2); ?> ريال
                                            </div>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo date('Y-m-d', strtotime($transaction['sale_date'])); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="pages/sales/index.php" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>
                            عرض جميع المبيعات
                        </a>
                    </div>
                    
                <?php else: ?>
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h5>لا توجد معاملات معلقة!</h5>
                        <p class="mb-0">جميع المبيعات مدفوعة بالكامل أو مسجلة كديون</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-5">
            <h2>⚖️ مركز إدارة الأرصدة الشامل! ⚖️</h2>
            <p class="lead">إدارة متكاملة لجميع أرصدة العملاء والديون</p>
            
            <div class="mt-4">
                <a href="index.php" class="btn btn-light btn-lg me-3">
                    <i class="fas fa-home me-2"></i>
                    الصفحة الرئيسية
                </a>
                <a href="customer-balance-fix-summary.php" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-info-circle me-2"></i>
                    دليل الاستخدام
                </a>
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحديث الصفحة كل دقيقة
        setTimeout(function() {
            location.reload();
        }, 60000);
        
        // إضافة تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card, .tool-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 150);
            });
        });
    </script>

</body>
</html>
