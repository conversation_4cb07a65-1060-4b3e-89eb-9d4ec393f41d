<?php
/**
 * تشخيص مشكلة إدارة أرصدة العملاء
 * Debug Customer Balance Management
 */

require_once "config/db_config.php";
require_once "includes/functions.php";
require_once "includes/advanced-balance-functions.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "تشخيص أرصدة العملاء";

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>$page_title</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>
        body { font-family: 'Cairo', sans-serif; padding: 20px; }
        .debug-section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
<div class='container'>
<h1>$page_title</h1>";

// 1. فحص هيكل جدول العملاء
echo "<div class='debug-section'>";
echo "<h3>1. فحص هيكل جدول العملاء</h3>";

try {
    $columns_query = "SHOW COLUMNS FROM customers";
    $columns_result = $conn->query($columns_query);
    
    echo "<table class='table table-sm'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>افتراضي</th></tr>";
    
    $has_wallet = false;
    $has_debt = false;
    
    while ($column = $columns_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
        
        if ($column['Field'] == 'wallet_balance') $has_wallet = true;
        if ($column['Field'] == 'debt_balance') $has_debt = true;
    }
    echo "</table>";
    
    if ($has_wallet && $has_debt) {
        echo "<p class='success'>✓ النظام المتقدم مفعل - الأعمدة موجودة</p>";
    } else {
        echo "<p class='error'>✗ النظام المتقدم غير مفعل - الأعمدة مفقودة</p>";
        echo "<p><a href='apply-balance-update-final.php' class='btn btn-warning'>تفعيل النظام المتقدم</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>خطأ: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 2. فحص بيانات العملاء
echo "<div class='debug-section'>";
echo "<h3>2. فحص بيانات العملاء</h3>";

try {
    $customers_query = "SELECT id, name, 
                               COALESCE(wallet_balance, 0) as wallet_balance,
                               COALESCE(debt_balance, 0) as debt_balance,
                               balance as old_balance
                        FROM customers 
                        WHERE id > 1 
                        ORDER BY id 
                        LIMIT 10";
    $customers_result = $conn->query($customers_query);
    
    if ($customers_result->num_rows > 0) {
        echo "<table class='table table-sm'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>الرصيد القديم</th><th>الرصيد المحفوظ</th><th>الديون</th></tr>";
        
        while ($customer = $customers_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $customer['id'] . "</td>";
            echo "<td>" . htmlspecialchars($customer['name']) . "</td>";
            echo "<td>" . number_format($customer['old_balance'], 2) . "</td>";
            echo "<td>" . number_format($customer['wallet_balance'], 2) . "</td>";
            echo "<td>" . number_format($customer['debt_balance'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>لا توجد عملاء في النظام</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>خطأ: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 3. فحص المبيعات
echo "<div class='debug-section'>";
echo "<h3>3. فحص المبيعات الحديثة</h3>";

try {
    $sales_query = "SELECT s.id, s.sale_number, s.customer_id, c.name as customer_name,
                           s.final_amount, s.paid_amount, 
                           (s.final_amount - s.paid_amount) as debt_amount,
                           s.sale_date
                    FROM sales s
                    LEFT JOIN customers c ON s.customer_id = c.id
                    WHERE s.customer_id > 1
                    ORDER BY s.created_at DESC
                    LIMIT 10";
    $sales_result = $conn->query($sales_query);
    
    if ($sales_result->num_rows > 0) {
        echo "<table class='table table-sm'>";
        echo "<tr><th>رقم المبيعة</th><th>العميل</th><th>المبلغ الإجمالي</th><th>المدفوع</th><th>المتبقي</th><th>التاريخ</th></tr>";
        
        while ($sale = $sales_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($sale['sale_number']) . "</td>";
            echo "<td>" . htmlspecialchars($sale['customer_name']) . "</td>";
            echo "<td>" . number_format($sale['final_amount'], 2) . "</td>";
            echo "<td>" . number_format($sale['paid_amount'], 2) . "</td>";
            echo "<td>" . number_format($sale['debt_amount'], 2) . "</td>";
            echo "<td>" . $sale['sale_date'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>لا توجد مبيعات في النظام</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>خطأ: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 4. فحص دالة getCustomersBalanceSummary
echo "<div class='debug-section'>";
echo "<h3>4. فحص دالة ملخص الأرصدة</h3>";

try {
    $summary = getCustomersBalanceSummary();
    
    echo "<table class='table table-sm'>";
    echo "<tr><th>المؤشر</th><th>القيمة</th></tr>";
    foreach ($summary as $key => $value) {
        echo "<tr>";
        echo "<td>" . $key . "</td>";
        echo "<td>" . (is_numeric($value) ? number_format($value, 2) : $value) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p class='error'>خطأ في دالة getCustomersBalanceSummary: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 5. اختبار المزامنة التلقائية
echo "<div class='debug-section'>";
echo "<h3>5. اختبار المزامنة التلقائية</h3>";

try {
    // البحث عن عميل له مبيعات
    $test_customer_query = "SELECT c.id, c.name, COUNT(s.id) as sales_count
                           FROM customers c
                           LEFT JOIN sales s ON c.id = s.customer_id
                           WHERE c.id > 1
                           GROUP BY c.id, c.name
                           HAVING sales_count > 0
                           LIMIT 1";
    $test_result = $conn->query($test_customer_query);
    
    if ($test_result->num_rows > 0) {
        $test_customer = $test_result->fetch_assoc();
        $customer_id = $test_customer['id'];
        
        echo "<p>اختبار المزامنة للعميل: " . htmlspecialchars($test_customer['name']) . " (ID: $customer_id)</p>";
        
        // تشغيل المزامنة
        $sync_result = autoSyncCustomerBalance($customer_id);
        
        if ($sync_result) {
            echo "<p class='success'>✓ تم تشغيل المزامنة بنجاح</p>";
        } else {
            echo "<p class='error'>✗ فشل في تشغيل المزامنة</p>";
        }
        
    } else {
        echo "<p class='warning'>لا توجد عملاء لديهم مبيعات للاختبار</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>خطأ في اختبار المزامنة: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 6. روابط مفيدة
echo "<div class='debug-section'>";
echo "<h3>6. روابط مفيدة</h3>";
echo "<p>";
echo "<a href='pages/customers/advanced-balance.php' class='btn btn-primary me-2'>إدارة أرصدة العملاء</a>";
echo "<a href='sync-customer-balances.php' class='btn btn-warning me-2'>مزامنة الأرصدة</a>";
echo "<a href='balance-management-center.php' class='btn btn-info me-2'>مركز إدارة الأرصدة</a>";
echo "<a href='test-auto-sync.php' class='btn btn-success'>اختبار المزامنة</a>";
echo "</p>";
echo "</div>";

echo "</div></body></html>";
?>
