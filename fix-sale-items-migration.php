<?php
/**
 * إصلاح ونقل بيانات الأصناف من sale_items إلى sale_details
 * Fix and Migrate Sale Items Data
 */

// استدعاء ملفات الإعدادات
require_once "config/db_config.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "إصلاح ونقل بيانات الأصناف";
$migration_results = [];
$errors = [];

// معالجة النقل
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['migrate_data'])) {
    try {
        $conn->begin_transaction();
        
        // التحقق من وجود الجداول
        $check_sale_items = $conn->query("SHOW TABLES LIKE 'sale_items'");
        $check_sale_details = $conn->query("SHOW TABLES LIKE 'sale_details'");
        
        $has_sale_items = $check_sale_items->num_rows > 0;
        $has_sale_details = $check_sale_details->num_rows > 0;
        
        if (!$has_sale_items) {
            throw new Exception("جدول sale_items غير موجود");
        }
        
        if (!$has_sale_details) {
            throw new Exception("جدول sale_details غير موجود - يجب تطبيق التحديثات أولاً");
        }
        
        // الحصول على البيانات من sale_items
        $items_query = "SELECT * FROM sale_items ORDER BY sale_id, id";
        $items_result = $conn->query($items_query);
        
        if ($items_result->num_rows > 0) {
            $migrated_count = 0;
            $skipped_count = 0;
            
            while ($item = $items_result->fetch_assoc()) {
                // التحقق من وجود البيانات في sale_details
                $check_existing = $conn->prepare("SELECT COUNT(*) as count FROM sale_details WHERE sale_id = ? AND product_id = ?");
                $check_existing->bind_param("ii", $item['sale_id'], $item['product_id']);
                $check_existing->execute();
                $existing_count = $check_existing->get_result()->fetch_assoc()['count'];
                
                if ($existing_count == 0) {
                    // نقل البيانات
                    $insert_query = "INSERT INTO sale_details (sale_id, product_id, quantity, unit_price, total_price, final_price, created_at) 
                                    VALUES (?, ?, ?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($insert_query);
                    
                    $final_price = $item['total_price']; // نفس إجمالي السعر
                    $created_at = $item['created_at'] ?? date('Y-m-d H:i:s');
                    
                    $stmt->bind_param("iidddds", 
                        $item['sale_id'], 
                        $item['product_id'], 
                        $item['quantity'], 
                        $item['unit_price'], 
                        $item['total_price'], 
                        $final_price,
                        $created_at
                    );
                    $stmt->execute();
                    $migrated_count++;
                } else {
                    $skipped_count++;
                }
            }
            
            $migration_results[] = "تم نقل $migrated_count صنف بنجاح";
            if ($skipped_count > 0) {
                $migration_results[] = "تم تخطي $skipped_count صنف (موجود مسبقاً)";
            }
        } else {
            $migration_results[] = "لا توجد بيانات في جدول sale_items للنقل";
        }
        
        $conn->commit();
        
    } catch (Exception $e) {
        $conn->rollback();
        $errors[] = "خطأ في النقل: " . $e->getMessage();
    }
}

// فحص حالة الجداول والبيانات
$status = [];

try {
    // فحص الجداول
    $check_sale_items = $conn->query("SHOW TABLES LIKE 'sale_items'");
    $check_sale_details = $conn->query("SHOW TABLES LIKE 'sale_details'");
    
    $status['has_sale_items'] = $check_sale_items->num_rows > 0;
    $status['has_sale_details'] = $check_sale_details->num_rows > 0;
    
    // عدد البيانات في كل جدول
    if ($status['has_sale_items']) {
        $count_items = $conn->query("SELECT COUNT(*) as count FROM sale_items")->fetch_assoc()['count'];
        $status['sale_items_count'] = $count_items;
    }
    
    if ($status['has_sale_details']) {
        $count_details = $conn->query("SELECT COUNT(*) as count FROM sale_details")->fetch_assoc()['count'];
        $status['sale_details_count'] = $count_details;
    }
    
    // فحص المبيعات التي لها أصناف في sale_items ولكن ليس في sale_details
    if ($status['has_sale_items'] && $status['has_sale_details']) {
        $missing_query = "SELECT DISTINCT si.sale_id 
                         FROM sale_items si 
                         LEFT JOIN sale_details sd ON si.sale_id = sd.sale_id 
                         WHERE sd.sale_id IS NULL";
        $missing_result = $conn->query($missing_query);
        $status['missing_sales'] = $missing_result->num_rows;
    }
    
} catch (Exception $e) {
    $errors[] = "خطأ في فحص الحالة: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .status-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        .status-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .status-danger {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-exchange-alt me-3"></i>
                إصلاح ونقل بيانات الأصناف
            </h1>
            <p class="lead">حل مشكلة اختفاء الأصناف من الفاتورة</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($migration_results)): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>نتائج النقل:</h5>
                <ul class="mb-0">
                    <?php foreach ($migration_results as $result): ?>
                        <li><?php echo htmlspecialchars($result); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle me-2"></i>أخطاء:</h5>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- حالة الجداول -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-database me-2"></i>حالة الجداول والبيانات</h3>
            </div>
            <div class="card-body">
                
                <div class="status-item <?php echo $status['has_sale_items'] ? 'status-success' : 'status-danger'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $status['has_sale_items'] ? 'check' : 'times'; ?> me-2"></i>
                        جدول sale_items (القديم)
                    </h6>
                    <p class="mb-0">
                        <?php if ($status['has_sale_items']): ?>
                            موجود - يحتوي على <?php echo $status['sale_items_count']; ?> صنف
                        <?php else: ?>
                            غير موجود
                        <?php endif; ?>
                    </p>
                </div>
                
                <div class="status-item <?php echo $status['has_sale_details'] ? 'status-success' : 'status-danger'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $status['has_sale_details'] ? 'check' : 'times'; ?> me-2"></i>
                        جدول sale_details (الجديد)
                    </h6>
                    <p class="mb-0">
                        <?php if ($status['has_sale_details']): ?>
                            موجود - يحتوي على <?php echo $status['sale_details_count']; ?> صنف
                        <?php else: ?>
                            غير موجود - يجب تطبيق التحديثات أولاً
                        <?php endif; ?>
                    </p>
                </div>
                
                <?php if (isset($status['missing_sales'])): ?>
                    <div class="status-item <?php echo $status['missing_sales'] > 0 ? 'status-warning' : 'status-success'; ?>">
                        <h6>
                            <i class="fas fa-<?php echo $status['missing_sales'] > 0 ? 'exclamation-triangle' : 'check'; ?> me-2"></i>
                            المبيعات المفقودة الأصناف
                        </h6>
                        <p class="mb-0">
                            <?php if ($status['missing_sales'] > 0): ?>
                                يوجد <?php echo $status['missing_sales']; ?> مبيعة تحتاج نقل أصنافها
                            <?php else: ?>
                                جميع المبيعات لها أصناف في الجدول الجديد
                            <?php endif; ?>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- شرح المشكلة -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-question-circle me-2"></i>شرح المشكلة</h3>
            </div>
            <div class="card-body">
                <h5>لماذا اختفت الأصناف من الفاتورة؟</h5>
                <ul>
                    <li><strong>النظام القديم:</strong> كان يحفظ الأصناف في جدول <code>sale_items</code></li>
                    <li><strong>النظام الجديد:</strong> يحفظ الأصناف في جدول <code>sale_details</code></li>
                    <li><strong>ملفات الطباعة:</strong> تم تحديثها للبحث في الجدولين</li>
                    <li><strong>المبيعات القديمة:</strong> أصنافها ما زالت في الجدول القديم</li>
                </ul>
                
                <h5 class="mt-3">الحل:</h5>
                <p>نقل البيانات من <code>sale_items</code> إلى <code>sale_details</code> للمبيعات الموجودة</p>
            </div>
        </div>

        <!-- إجراء النقل -->
        <?php if ($status['has_sale_items'] && $status['has_sale_details'] && isset($status['missing_sales']) && $status['missing_sales'] > 0): ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-play me-2"></i>نقل البيانات</h3>
                </div>
                <div class="card-body">
                    <p>يوجد <?php echo $status['missing_sales']; ?> مبيعة تحتاج نقل أصنافها من الجدول القديم إلى الجديد.</p>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من نقل البيانات؟')">
                        <button type="submit" name="migrate_data" class="btn btn-success btn-lg">
                            <i class="fas fa-exchange-alt me-2"></i>
                            نقل البيانات الآن
                        </button>
                    </form>
                </div>
            </div>
        <?php elseif ($status['has_sale_details'] && (!isset($status['missing_sales']) || $status['missing_sales'] == 0)): ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-check-circle me-2"></i>تم الإصلاح!</h3>
                </div>
                <div class="card-body">
                    <p>جميع البيانات موجودة في الجدول الصحيح. الأصناف ستظهر في الفواتير الآن.</p>
                    
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="pages/sales/index.php" class="btn btn-primary btn-lg me-md-2">
                            <i class="fas fa-list me-2"></i>
                            مراجعة المبيعات
                        </a>
                        <a href="pages/sales/add.php" class="btn btn-success btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            مبيعة جديدة
                        </a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h3><i class="fas fa-exclamation-triangle me-2"></i>يجب تطبيق التحديثات أولاً</h3>
                </div>
                <div class="card-body">
                    <p>لحل هذه المشكلة، يجب تطبيق تحديثات قاعدة البيانات أولاً لإنشاء جدول <code>sale_details</code>.</p>
                    
                    <a href="apply-balance-update-final.php" class="btn btn-danger btn-lg">
                        <i class="fas fa-database me-2"></i>
                        تطبيق التحديثات
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <h2>🔧 إصلاح مشكلة الأصناف المختفية! 🔧</h2>
            <p class="lead">استعادة عرض الأصناف في جميع الفواتير</p>
            
            <a href="index.php" class="btn btn-light btn-lg mt-3">
                <i class="fas fa-home me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
