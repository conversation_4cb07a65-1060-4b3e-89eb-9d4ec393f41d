<?php
/**
 * إنشاء جدول تفاصيل المبيعات
 * Create Sale Details Table
 */

// استدعاء ملفات الإعدادات
require_once "config/db_config.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "إنشاء جدول تفاصيل المبيعات";
$table_created = false;
$errors = [];
$success_messages = [];

// معالجة إنشاء الجدول
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_table'])) {
    try {
        $conn->begin_transaction();
        
        // التحقق من وجود الجدول
        $check_table = $conn->query("SHOW TABLES LIKE 'sale_details'");
        
        if ($check_table->num_rows == 0) {
            // إنشاء جدول تفاصيل المبيعات
            $create_table_sql = "
            CREATE TABLE `sale_details` (
              `id` INT(11) NOT NULL AUTO_INCREMENT,
              `sale_id` INT(11) NOT NULL COMMENT 'معرف المبيعة',
              `product_id` INT(11) NOT NULL COMMENT 'معرف المنتج',
              `quantity` DECIMAL(10,3) NOT NULL COMMENT 'الكمية',
              `unit_price` DECIMAL(10,2) NOT NULL COMMENT 'سعر الوحدة',
              `total_price` DECIMAL(10,2) NOT NULL COMMENT 'إجمالي السعر',
              `discount_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'مبلغ الخصم',
              `final_price` DECIMAL(10,2) NOT NULL COMMENT 'السعر النهائي',
              `notes` TEXT NULL COMMENT 'ملاحظات',
              `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `sale_id` (`sale_id`),
              KEY `product_id` (`product_id`),
              FOREIGN KEY (`sale_id`) REFERENCES `sales`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
              FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='تفاصيل المبيعات'";
            
            $conn->query($create_table_sql);
            $success_messages[] = "تم إنشاء جدول sale_details بنجاح";
        } else {
            $success_messages[] = "جدول sale_details موجود بالفعل";
        }
        
        // التحقق من وجود جدول sales_items (اسم بديل محتمل)
        $check_sales_items = $conn->query("SHOW TABLES LIKE 'sales_items'");
        
        if ($check_sales_items->num_rows == 0) {
            // إنشاء جدول sales_items كبديل
            $create_sales_items_sql = "
            CREATE TABLE `sales_items` (
              `id` INT(11) NOT NULL AUTO_INCREMENT,
              `sale_id` INT(11) NOT NULL COMMENT 'معرف المبيعة',
              `product_id` INT(11) NOT NULL COMMENT 'معرف المنتج',
              `quantity` DECIMAL(10,3) NOT NULL COMMENT 'الكمية',
              `unit_price` DECIMAL(10,2) NOT NULL COMMENT 'سعر الوحدة',
              `total_price` DECIMAL(10,2) NOT NULL COMMENT 'إجمالي السعر',
              `discount_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'مبلغ الخصم',
              `final_price` DECIMAL(10,2) NOT NULL COMMENT 'السعر النهائي',
              `notes` TEXT NULL COMMENT 'ملاحظات',
              `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `sale_id` (`sale_id`),
              KEY `product_id` (`product_id`),
              FOREIGN KEY (`sale_id`) REFERENCES `sales`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
              FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='عناصر المبيعات'";
            
            $conn->query($create_sales_items_sql);
            $success_messages[] = "تم إنشاء جدول sales_items بنجاح";
        } else {
            $success_messages[] = "جدول sales_items موجود بالفعل";
        }
        
        // التحقق من وجود جدول purchase_details أيضاً
        $check_purchase_details = $conn->query("SHOW TABLES LIKE 'purchase_details'");
        
        if ($check_purchase_details->num_rows == 0) {
            // إنشاء جدول تفاصيل المشتريات
            $create_purchase_details_sql = "
            CREATE TABLE `purchase_details` (
              `id` INT(11) NOT NULL AUTO_INCREMENT,
              `purchase_id` INT(11) NOT NULL COMMENT 'معرف المشترى',
              `product_id` INT(11) NOT NULL COMMENT 'معرف المنتج',
              `quantity` DECIMAL(10,3) NOT NULL COMMENT 'الكمية',
              `unit_price` DECIMAL(10,2) NOT NULL COMMENT 'سعر الوحدة',
              `total_price` DECIMAL(10,2) NOT NULL COMMENT 'إجمالي السعر',
              `discount_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'مبلغ الخصم',
              `final_price` DECIMAL(10,2) NOT NULL COMMENT 'السعر النهائي',
              `notes` TEXT NULL COMMENT 'ملاحظات',
              `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `purchase_id` (`purchase_id`),
              KEY `product_id` (`product_id`),
              FOREIGN KEY (`purchase_id`) REFERENCES `purchases`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
              FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='تفاصيل المشتريات'";
            
            $conn->query($create_purchase_details_sql);
            $success_messages[] = "تم إنشاء جدول purchase_details بنجاح";
        } else {
            $success_messages[] = "جدول purchase_details موجود بالفعل";
        }
        
        $conn->commit();
        $table_created = true;
        
    } catch (Exception $e) {
        $conn->rollback();
        $errors[] = "خطأ في إنشاء الجداول: " . $e->getMessage();
    }
}

// فحص حالة الجداول
$table_status = [];

try {
    $tables_to_check = ['sale_details', 'sales_items', 'purchase_details'];
    
    foreach ($tables_to_check as $table) {
        $check_table = $conn->query("SHOW TABLES LIKE '$table'");
        $table_status[$table] = $check_table->num_rows > 0;
    }
    
} catch (Exception $e) {
    $errors[] = "خطأ في فحص الجداول: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .status-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        .status-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-table me-3"></i>
                إنشاء جدول تفاصيل المبيعات
            </h1>
            <p class="lead">إصلاح مشكلة جدول sale_details المفقود</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($success_messages)): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>رسائل النجاح:</h5>
                <ul class="mb-0">
                    <?php foreach ($success_messages as $message): ?>
                        <li><?php echo htmlspecialchars($message); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle me-2"></i>رسائل الخطأ:</h5>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- حالة الجداول -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-database me-2"></i>حالة الجداول</h3>
            </div>
            <div class="card-body">
                
                <div class="status-item <?php echo $table_status['sale_details'] ? 'status-success' : 'status-warning'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $table_status['sale_details'] ? 'check' : 'times'; ?> me-2"></i>
                        جدول sale_details
                    </h6>
                    <p class="mb-0">
                        <?php echo $table_status['sale_details'] ? 'موجود ومُحدث' : 'غير موجود - يحتاج إنشاء'; ?>
                    </p>
                </div>
                
                <div class="status-item <?php echo $table_status['sales_items'] ? 'status-success' : 'status-warning'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $table_status['sales_items'] ? 'check' : 'times'; ?> me-2"></i>
                        جدول sales_items
                    </h6>
                    <p class="mb-0">
                        <?php echo $table_status['sales_items'] ? 'موجود ومُحدث' : 'غير موجود - يحتاج إنشاء'; ?>
                    </p>
                </div>
                
                <div class="status-item <?php echo $table_status['purchase_details'] ? 'status-success' : 'status-warning'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $table_status['purchase_details'] ? 'check' : 'times'; ?> me-2"></i>
                        جدول purchase_details
                    </h6>
                    <p class="mb-0">
                        <?php echo $table_status['purchase_details'] ? 'موجود ومُحدث' : 'غير موجود - يحتاج إنشاء'; ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- معلومات الجداول -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-info-circle me-2"></i>معلومات الجداول</h3>
            </div>
            <div class="card-body">
                <h5>ما الذي سيتم إنشاؤه؟</h5>
                <ul>
                    <li><strong>sale_details:</strong> جدول تفاصيل المبيعات (المنتجات المباعة)</li>
                    <li><strong>sales_items:</strong> جدول بديل لعناصر المبيعات</li>
                    <li><strong>purchase_details:</strong> جدول تفاصيل المشتريات</li>
                </ul>
                
                <h5 class="mt-3">هيكل الجداول:</h5>
                <ul>
                    <li>معرف المبيعة/المشترى</li>
                    <li>معرف المنتج</li>
                    <li>الكمية</li>
                    <li>سعر الوحدة</li>
                    <li>إجمالي السعر</li>
                    <li>مبلغ الخصم</li>
                    <li>السعر النهائي</li>
                    <li>ملاحظات</li>
                    <li>تواريخ الإنشاء والتحديث</li>
                </ul>
            </div>
        </div>

        <!-- إنشاء الجداول -->
        <?php if (!$table_created && (!$table_status['sale_details'] || !$table_status['sales_items'] || !$table_status['purchase_details'])): ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-play me-2"></i>إنشاء الجداول المطلوبة</h3>
                </div>
                <div class="card-body">
                    <p>انقر على الزر أدناه لإنشاء جميع الجداول المطلوبة:</p>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من إنشاء الجداول؟')">
                        <button type="submit" name="create_table" class="btn btn-success btn-lg">
                            <i class="fas fa-table me-2"></i>
                            إنشاء الجداول الآن
                        </button>
                    </form>
                </div>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-check-circle me-2"></i>تم إنشاء الجداول بنجاح!</h3>
                </div>
                <div class="card-body">
                    <p>تم إنشاء جميع الجداول المطلوبة. يمكنك الآن استخدام نظام المبيعات.</p>
                    
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="pages/sales/add-with-advanced-balance.php" class="btn btn-primary btn-lg me-md-2">
                            <i class="fas fa-cash-register me-2"></i>
                            تجربة نظام المبيعات
                        </a>
                        <a href="test-sales-system.php" class="btn btn-info btn-lg me-md-2">
                            <i class="fas fa-vial me-2"></i>
                            اختبار النظام
                        </a>
                        <a href="index.php" class="btn btn-secondary btn-lg">
                            <i class="fas fa-home me-2"></i>
                            الصفحة الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <h2>🔧 إصلاح مشكلة الجداول المفقودة! 🔧</h2>
            <p class="lead">إنشاء جميع الجداول المطلوبة لنظام المبيعات</p>
            
            <div class="alert alert-warning mt-4">
                <strong>ملاحظة:</strong> بعد إنشاء الجداول، يمكنك حذف هذا الملف:
                <br><code>create-sale-details-table.php</code>
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
