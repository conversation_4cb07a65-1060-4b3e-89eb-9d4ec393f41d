<?php
/**
 * إصلاح جدول الخزينة وإضافة الأعمدة المفقودة
 * Fix Treasury Table and Add Missing Columns
 */

// استدعاء ملفات الإعدادات
require_once "config/db_config.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "إصلاح جدول الخزينة";
$updates = [];
$errors = [];

// معالجة التحديث
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_table'])) {
    try {
        $conn->begin_transaction();
        
        // التحقق من وجود الأعمدة وإضافة المفقود منها
        $columns_to_add = [
            'reference_type' => "ALTER TABLE treasury ADD COLUMN reference_type VARCHAR(50) NULL COMMENT 'نوع المرجع' AFTER transaction_type",
            'payment_method' => "ALTER TABLE treasury ADD COLUMN payment_method VARCHAR(50) NULL DEFAULT 'cash' COMMENT 'طريقة الدفع' AFTER reference_type",
            'notes' => "ALTER TABLE treasury ADD COLUMN notes TEXT NULL COMMENT 'ملاحظات' AFTER description",
            'status' => "ALTER TABLE treasury ADD COLUMN status ENUM('pending', 'completed', 'cancelled') DEFAULT 'completed' COMMENT 'حالة المعاملة' AFTER notes"
        ];
        
        foreach ($columns_to_add as $column => $sql) {
            $check_query = "SHOW COLUMNS FROM treasury LIKE '$column'";
            $result = $conn->query($check_query);
            
            if ($result->num_rows == 0) {
                $conn->query($sql);
                $updates[] = "تم إضافة العمود: $column";
            } else {
                $updates[] = "العمود $column موجود بالفعل";
            }
        }
        
        // تحديث البيانات الموجودة لتحديد reference_type
        $update_reference_type = "UPDATE treasury SET reference_type = 
            CASE 
                WHEN description LIKE '%مورد%' AND description LIKE '%دفع%' THEN 'supplier_payment'
                WHEN description LIKE '%مورد%' AND description LIKE '%رصيد%' THEN 'supplier_debt'
                WHEN description LIKE '%عميل%' AND description LIKE '%دفع%' THEN 'customer_payment'
                WHEN description LIKE '%عميل%' AND description LIKE '%رصيد%' THEN 'customer_debt'
                WHEN transaction_type = 'sales' THEN 'sale'
                WHEN transaction_type = 'purchases' THEN 'purchase'
                WHEN transaction_type = 'expenses' THEN 'expense'
                WHEN transaction_type = 'deposit' THEN 'deposit'
                WHEN transaction_type = 'withdraw' THEN 'withdraw'
                ELSE transaction_type
            END
            WHERE reference_type IS NULL";
        
        $conn->query($update_reference_type);
        $updates[] = "تم تحديث أنواع المراجع للبيانات الموجودة";
        
        // إضافة مؤشرات للأداء
        $indexes = [
            'idx_treasury_reference' => "CREATE INDEX idx_treasury_reference ON treasury(reference_type, reference_id)",
            'idx_treasury_date' => "CREATE INDEX idx_treasury_date ON treasury(transaction_date)",
            'idx_treasury_type' => "CREATE INDEX idx_treasury_type ON treasury(transaction_type)"
        ];
        
        foreach ($indexes as $index_name => $sql) {
            try {
                $conn->query($sql);
                $updates[] = "تم إضافة المؤشر: $index_name";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                    $updates[] = "تحذير في المؤشر $index_name: " . $e->getMessage();
                } else {
                    $updates[] = "المؤشر $index_name موجود بالفعل";
                }
            }
        }
        
        $conn->commit();
        
    } catch (Exception $e) {
        $conn->rollback();
        $errors[] = "خطأ في التحديث: " . $e->getMessage();
    }
}

// فحص حالة الجدول
$table_status = [];

try {
    // فحص وجود الجدول
    $check_table = $conn->query("SHOW TABLES LIKE 'treasury'");
    $table_status['table_exists'] = $check_table->num_rows > 0;
    
    if ($table_status['table_exists']) {
        // فحص الأعمدة
        $required_columns = ['transaction_type', 'reference_id', 'amount', 'balance_after', 'description', 'user_id', 'transaction_date', 'reference_type', 'payment_method', 'notes', 'status'];
        
        foreach ($required_columns as $column) {
            $check_query = "SHOW COLUMNS FROM treasury LIKE '$column'";
            $result = $conn->query($check_query);
            $table_status["has_$column"] = $result->num_rows > 0;
        }
        
        // عدد المعاملات
        $count_query = "SELECT COUNT(*) as count FROM treasury";
        $count_result = $conn->query($count_query);
        $table_status['transactions_count'] = $count_result->fetch_assoc()['count'];
        
        // فحص البيانات التي تحتاج تحديث reference_type
        if ($table_status['has_reference_type']) {
            $null_ref_query = "SELECT COUNT(*) as count FROM treasury WHERE reference_type IS NULL";
            $null_ref_result = $conn->query($null_ref_query);
            $table_status['null_reference_count'] = $null_ref_result->fetch_assoc()['count'];
        }
    }
    
} catch (Exception $e) {
    $errors[] = "خطأ في فحص الجدول: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .status-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        .status-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .status-danger {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-university me-3"></i>
                إصلاح جدول الخزينة
            </h1>
            <p class="lead">إضافة الأعمدة المفقودة وإصلاح مشكلة "reference_type"</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($updates)): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>تحديثات مطبقة:</h5>
                <ul class="mb-0">
                    <?php foreach ($updates as $update): ?>
                        <li><?php echo htmlspecialchars($update); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle me-2"></i>أخطاء:</h5>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- حالة الجدول -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-database me-2"></i>حالة جدول الخزينة</h3>
            </div>
            <div class="card-body">
                
                <?php if ($table_status['table_exists']): ?>
                    <div class="status-item status-success">
                        <h6><i class="fas fa-check me-2"></i>جدول treasury موجود</h6>
                        <p class="mb-0">يحتوي على <?php echo $table_status['transactions_count']; ?> معاملة</p>
                    </div>
                    
                    <h6 class="mt-3">الأعمدة الأساسية:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <?php foreach (['transaction_type', 'reference_id', 'amount', 'balance_after', 'description', 'user_id'] as $column): ?>
                                <div class="status-item <?php echo $table_status["has_$column"] ? 'status-success' : 'status-danger'; ?>">
                                    <span><?php echo $column; ?></span>
                                    <i class="fas fa-<?php echo $table_status["has_$column"] ? 'check text-success' : 'times text-danger'; ?> float-end"></i>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="col-md-6">
                            <?php foreach (['transaction_date', 'reference_type', 'payment_method', 'notes', 'status'] as $column): ?>
                                <div class="status-item <?php echo $table_status["has_$column"] ? 'status-success' : 'status-warning'; ?>">
                                    <span><?php echo $column; ?></span>
                                    <i class="fas fa-<?php echo $table_status["has_$column"] ? 'check text-success' : 'times text-warning'; ?> float-end"></i>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <?php if (isset($table_status['null_reference_count']) && $table_status['null_reference_count'] > 0): ?>
                        <div class="status-item status-warning mt-3">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>بيانات تحتاج تحديث</h6>
                            <p class="mb-0"><?php echo $table_status['null_reference_count']; ?> معاملة تحتاج تحديد نوع المرجع</p>
                        </div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <div class="status-item status-danger">
                        <h6><i class="fas fa-times me-2"></i>جدول treasury غير موجود</h6>
                        <p class="mb-0">يجب إنشاء الجدول أولاً</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- شرح المشكلة -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-question-circle me-2"></i>شرح المشكلة</h3>
            </div>
            <div class="card-body">
                <h5>خطأ "Unknown column 'reference_type' in 'where clause'":</h5>
                <ul>
                    <li><strong>السبب:</strong> الكود يحاول البحث في عمود <code>reference_type</code> غير موجود</li>
                    <li><strong>التأثير:</strong> فشل في عرض حركات الموردين والعملاء</li>
                    <li><strong>الحل:</strong> إضافة العمود المفقود وتحديث البيانات الموجودة</li>
                </ul>
                
                <h5 class="mt-3">الأعمدة المطلوبة:</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>أساسية:</h6>
                        <ul>
                            <li><code>transaction_type</code> - نوع المعاملة</li>
                            <li><code>reference_id</code> - معرف المرجع</li>
                            <li><code>amount</code> - المبلغ</li>
                            <li><code>balance_after</code> - الرصيد بعد المعاملة</li>
                            <li><code>description</code> - الوصف</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>إضافية:</h6>
                        <ul>
                            <li><code>reference_type</code> - نوع المرجع</li>
                            <li><code>payment_method</code> - طريقة الدفع</li>
                            <li><code>notes</code> - ملاحظات</li>
                            <li><code>status</code> - حالة المعاملة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- إصلاح الجدول -->
        <?php 
        $needs_fix = false;
        if ($table_status['table_exists']) {
            $required_for_fix = ['reference_type', 'payment_method', 'notes', 'status'];
            foreach ($required_for_fix as $col) {
                if (!$table_status["has_$col"]) {
                    $needs_fix = true;
                    break;
                }
            }
            
            // أو إذا كانت هناك بيانات تحتاج تحديث
            if (isset($table_status['null_reference_count']) && $table_status['null_reference_count'] > 0) {
                $needs_fix = true;
            }
        }
        ?>
        
        <?php if ($needs_fix): ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-wrench me-2"></i>إصلاح الجدول</h3>
                </div>
                <div class="card-body">
                    <p>سيتم تطبيق الإصلاحات التالية:</p>
                    <ul>
                        <?php if (!$table_status['has_reference_type']): ?><li>إضافة عمود <code>reference_type</code> - نوع المرجع</li><?php endif; ?>
                        <?php if (!$table_status['has_payment_method']): ?><li>إضافة عمود <code>payment_method</code> - طريقة الدفع</li><?php endif; ?>
                        <?php if (!$table_status['has_notes']): ?><li>إضافة عمود <code>notes</code> - ملاحظات</li><?php endif; ?>
                        <?php if (!$table_status['has_status']): ?><li>إضافة عمود <code>status</code> - حالة المعاملة</li><?php endif; ?>
                        <?php if (isset($table_status['null_reference_count']) && $table_status['null_reference_count'] > 0): ?>
                            <li>تحديث <?php echo $table_status['null_reference_count']; ?> معاملة لتحديد نوع المرجع</li>
                        <?php endif; ?>
                        <li>إضافة مؤشرات للأداء</li>
                    </ul>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من إصلاح الجدول؟')">
                        <button type="submit" name="fix_table" class="btn btn-success btn-lg">
                            <i class="fas fa-tools me-2"></i>
                            إصلاح الجدول الآن
                        </button>
                    </form>
                </div>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-check-circle me-2"></i>الجدول سليم!</h3>
                </div>
                <div class="card-body">
                    <p>جميع الأعمدة المطلوبة موجودة والبيانات محدثة. يمكنك الآن استخدام النظام بدون مشاكل.</p>
                    
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="pages/suppliers/index.php" class="btn btn-primary btn-lg me-md-2">
                            <i class="fas fa-truck me-2"></i>
                            إدارة الموردين
                        </a>
                        <a href="pages/treasury/index.php" class="btn btn-info btn-lg">
                            <i class="fas fa-university me-2"></i>
                            إدارة الخزينة
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <h2>🏦 إصلاح جدول الخزينة! 🏦</h2>
            <p class="lead">حل مشكلة الأعمدة المفقودة في نظام الخزينة</p>
            
            <a href="index.php" class="btn btn-light btn-lg mt-3">
                <i class="fas fa-home me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
