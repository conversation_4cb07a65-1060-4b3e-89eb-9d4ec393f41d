<?php
/**
 * صفحة المبيعات الرئيسية
 * Sales Main Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "إدارة المبيعات";
$page_icon = "fas fa-shopping-cart";

// معالجة حذف المبيعة
if (isset($_POST['delete_sale'])) {
    $sale_id = clean($conn, $_POST['sale_id']);

    try {
        $conn->begin_transaction();

        // الحصول على معلومات المبيعة قبل الحذف للمزامنة
        $sale_info_query = "SELECT customer_id FROM sales WHERE id = ?";
        $sale_info_stmt = $conn->prepare($sale_info_query);
        $sale_info_stmt->bind_param("i", $sale_id);
        $sale_info_stmt->execute();
        $sale_info = $sale_info_stmt->get_result()->fetch_assoc();
        $customer_id = $sale_info['customer_id'] ?? 0;

        // حذف المبيعة
        $delete_query = "DELETE FROM sales WHERE id = ?";
        $stmt = $conn->prepare($delete_query);
        $stmt->bind_param("i", $sale_id);
        $stmt->execute();

        // مزامنة تلقائية لرصيد العميل بعد حذف المبيعة
        autoSyncCustomerBalance($customer_id);

        $conn->commit();
        $_SESSION['success_message'] = "تم حذف المبيعة بنجاح";

    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['error_message'] = "خطأ في حذف المبيعة: " . $e->getMessage();
    }

    header("Location: index.php");
    exit();
}

// الحصول على المبيعات مع الفلترة والبحث
$search = isset($_GET['search']) ? clean($conn, $_GET['search']) : '';
$date_from = isset($_GET['date_from']) ? clean($conn, $_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? clean($conn, $_GET['date_to']) : '';
$customer_id = isset($_GET['customer_id']) ? clean($conn, $_GET['customer_id']) : '';

$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "(s.sale_number LIKE ? OR c.name LIKE ? OR u.name LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= 'sss';
}

if (!empty($date_from)) {
    $where_conditions[] = "s.sale_date >= ?";
    $params[] = $date_from;
    $types .= 's';
}

if (!empty($date_to)) {
    $where_conditions[] = "s.sale_date <= ?";
    $params[] = $date_to;
    $types .= 's';
}

if (!empty($customer_id)) {
    $where_conditions[] = "s.customer_id = ?";
    $params[] = $customer_id;
    $types .= 'i';
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
}

// استعلام المبيعات
$query = "SELECT s.*, c.name as customer_name, u.name as user_name 
          FROM sales s 
          LEFT JOIN customers c ON s.customer_id = c.id 
          LEFT JOIN users u ON s.user_id = u.id 
          $where_clause 
          ORDER BY s.created_at DESC";

if (!empty($params)) {
    $stmt = $conn->prepare($query);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $sales_result = $stmt->get_result();
} else {
    $sales_result = $conn->query($query);
}

// الحصول على قائمة العملاء للفلتر
$customers_query = "SELECT id, name FROM customers WHERE id > 1 ORDER BY name";
$customers_result = $conn->query($customers_query);

// حساب الإحصائيات
$stats_query = "SELECT 
                COUNT(*) as total_sales,
                COALESCE(SUM(final_amount), 0) as total_amount,
                COALESCE(SUM(paid_amount), 0) as total_paid,
                COALESCE(SUM(final_amount - paid_amount), 0) as total_remaining
                FROM sales s $where_clause";

if (!empty($params)) {
    $stmt = $conn->prepare($stats_query);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $stats = $stmt->get_result()->fetch_assoc();
} else {
    $stats = $conn->query($stats_query)->fetch_assoc();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .btn-action {
            padding: 0.25rem 0.5rem;
            margin: 0 0.1rem;
            border-radius: 0.375rem;
        }
        .search-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">إدارة وعرض جميع عمليات البيع</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="add-advanced.php" class="btn btn-light btn-lg me-2">
                        <i class="fas fa-barcode me-2"></i>
                        فاتورة متطورة
                    </a>
                    <a href="add.php" class="btn btn-outline-light btn-lg me-2">
                        <i class="fas fa-plus me-2"></i>
                        فاتورة عادية
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-primary mb-2">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                        <h3 class="card-title text-primary"><?php echo number_format($stats['total_sales']); ?></h3>
                        <p class="card-text text-muted">إجمالي المبيعات</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-success mb-2">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                        <h3 class="card-title text-success"><?php echo formatMoney($stats['total_amount']); ?></h3>
                        <p class="card-text text-muted">إجمالي المبلغ</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-info mb-2">
                            <i class="fas fa-hand-holding-usd fa-2x"></i>
                        </div>
                        <h3 class="card-title text-info"><?php echo formatMoney($stats['total_paid']); ?></h3>
                        <p class="card-text text-muted">المبلغ المدفوع</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-warning mb-2">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                        <h3 class="card-title text-warning"><?php echo formatMoney($stats['total_remaining']); ?></h3>
                        <p class="card-text text-muted">المبلغ المتبقي</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- البحث والفلترة -->
        <div class="search-container">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="رقم المبيعة، العميل، أو المستخدم">
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                <div class="col-md-3">
                    <label for="customer_id" class="form-label">العميل</label>
                    <select class="form-select" id="customer_id" name="customer_id">
                        <option value="">جميع العملاء</option>
                        <?php while ($customer = $customers_result->fetch_assoc()): ?>
                            <option value="<?php echo $customer['id']; ?>" 
                                    <?php echo ($customer_id == $customer['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($customer['name']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                    </div>
                </div>
            </form>
            
            <?php if (!empty($search) || !empty($date_from) || !empty($date_to) || !empty($customer_id)): ?>
                <div class="mt-3">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> مسح الفلاتر
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- جدول المبيعات -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>رقم المبيعة</th>
                            <th>التاريخ</th>
                            <th>العميل</th>
                            <th>المبلغ الإجمالي</th>
                            <th>الخصم</th>
                            <th>المبلغ النهائي</th>
                            <th>المدفوع</th>
                            <th>المتبقي</th>
                            <th>طريقة الدفع</th>
                            <th>المستخدم</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($sales_result->num_rows > 0): ?>
                            <?php while ($sale = $sales_result->fetch_assoc()): ?>
                                <?php 
                                $remaining = $sale['final_amount'] - $sale['paid_amount'];
                                $payment_status = '';
                                if ($remaining <= 0) {
                                    $payment_status = 'text-success';
                                } elseif ($sale['paid_amount'] > 0) {
                                    $payment_status = 'text-warning';
                                } else {
                                    $payment_status = 'text-danger';
                                }
                                ?>
                                <tr>
                                    <td>
                                        <strong class="text-primary"><?php echo htmlspecialchars($sale['sale_number']); ?></strong>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($sale['sale_date'])); ?></td>
                                    <td><?php echo htmlspecialchars($sale['customer_name'] ?? 'عميل نقدي'); ?></td>
                                    <td><?php echo formatMoney($sale['total_amount']); ?></td>
                                    <td class="text-danger"><?php echo formatMoney($sale['discount_amount']); ?></td>
                                    <td class="fw-bold"><?php echo formatMoney($sale['final_amount']); ?></td>
                                    <td class="text-success"><?php echo formatMoney($sale['paid_amount']); ?></td>
                                    <td class="<?php echo $payment_status; ?> fw-bold">
                                        <?php echo formatMoney($remaining); ?>
                                    </td>
                                    <td>
                                        <?php
                                        $payment_methods = [
                                            'cash' => 'نقدي',
                                            'card' => 'بطاقة',
                                            'bank_transfer' => 'تحويل بنكي'
                                        ];
                                        echo $payment_methods[$sale['payment_method']] ?? $sale['payment_method'];
                                        ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($sale['user_name']); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="view.php?id=<?php echo $sale['id']; ?>" 
                                               class="btn btn-info btn-action" 
                                               data-bs-toggle="tooltip" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $sale['id']; ?>" 
                                               class="btn btn-warning btn-action" 
                                               data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="print.php?id=<?php echo $sale['id']; ?>" 
                                               class="btn btn-secondary btn-action" 
                                               data-bs-toggle="tooltip" title="طباعة" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-danger btn-action" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#deleteModal<?php echo $sale['id']; ?>"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Modal حذف المبيعة -->
                                        <div class="modal fade" id="deleteModal<?php echo $sale['id']; ?>" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">تأكيد الحذف</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>هل أنت متأكد من حذف المبيعة رقم <strong><?php echo htmlspecialchars($sale['sale_number']); ?></strong>؟</p>
                                                        <p class="text-danger"><small>تحذير: هذا الإجراء لا يمكن التراجع عنه.</small></p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="sale_id" value="<?php echo $sale['id']; ?>">
                                                            <button type="submit" name="delete_sale" class="btn btn-danger">حذف</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="11" class="text-center py-4">
                                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد مبيعات</h5>
                                    <p class="text-muted">لم يتم العثور على أي مبيعات تطابق معايير البحث</p>
                                    <a href="add.php" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>إضافة مبيعة جديدة
                                    </a>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تفعيل التلميحات
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    </script>

</body>
</html>
