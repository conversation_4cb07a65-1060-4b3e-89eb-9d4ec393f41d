<?php
/**
 * إصلاح أرصدة العملاء وتسجيل الديون المفقودة
 * Fix Customer Balances and Register Missing Debts
 */

// استدعاء ملفات الإعدادات
require_once "config/db_config.php";
require_once "includes/functions.php";
require_once "includes/advanced-balance-functions.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "إصلاح أرصدة العملاء";
$fixes_applied = [];
$errors = [];

// معالجة الإصلاح
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_balances'])) {
    try {
        $conn->begin_transaction();
        
        // البحث عن المبيعات التي لها مبالغ متبقية ولم تُسجل كديون
        $missing_debts_query = "
            SELECT s.id, s.sale_number, s.customer_id, s.final_amount, s.paid_amount, 
                   (s.final_amount - s.paid_amount) as remaining_amount,
                   c.name as customer_name, c.balance as old_balance,
                   COALESCE(c.debt_balance, 0) as current_debt_balance,
                   s.sale_date, s.created_at
            FROM sales s 
            LEFT JOIN customers c ON s.customer_id = c.id 
            WHERE s.customer_id > 1 
            AND (s.final_amount - s.paid_amount) > 0.01
            AND s.customer_id IS NOT NULL
            ORDER BY s.created_at DESC
        ";
        
        $missing_debts_result = $conn->query($missing_debts_query);
        $total_fixed = 0;
        
        if ($missing_debts_result->num_rows > 0) {
            while ($sale = $missing_debts_result->fetch_assoc()) {
                $customer_id = $sale['customer_id'];
                $remaining_amount = $sale['remaining_amount'];
                $sale_id = $sale['id'];
                $customer_name = $sale['customer_name'];
                
                // التحقق من وجود أعمدة النظام المتقدم
                $check_columns = $conn->query("SHOW COLUMNS FROM customers LIKE 'debt_balance'");
                $has_advanced_columns = $check_columns->num_rows > 0;
                
                if ($has_advanced_columns) {
                    // استخدام النظام المتقدم
                    $update_debt_query = "UPDATE customers SET 
                                         debt_balance = debt_balance + ?,
                                         last_transaction_date = NOW()
                                         WHERE id = ?";
                    $stmt = $conn->prepare($update_debt_query);
                    $stmt->bind_param("di", $remaining_amount, $customer_id);
                    $stmt->execute();
                    
                    // تسجيل المعاملة في balance_transactions إذا كان الجدول موجود
                    $check_balance_table = $conn->query("SHOW TABLES LIKE 'balance_transactions'");
                    if ($check_balance_table->num_rows > 0) {
                        // الحصول على الأرصدة الحالية
                        $balance_query = "SELECT wallet_balance, debt_balance FROM customers WHERE id = ?";
                        $balance_stmt = $conn->prepare($balance_query);
                        $balance_stmt->bind_param("i", $customer_id);
                        $balance_stmt->execute();
                        $balance_result = $balance_stmt->get_result()->fetch_assoc();
                        
                        $wallet_before = $balance_result['wallet_balance'];
                        $debt_before = $balance_result['debt_balance'] - $remaining_amount;
                        
                        $transaction_query = "INSERT INTO balance_transactions 
                                            (entity_type, entity_id, transaction_type, amount,
                                             wallet_balance_before, wallet_balance_after,
                                             debt_balance_before, debt_balance_after,
                                             reference_type, reference_id, description, user_id, created_at)
                                            VALUES 
                                            ('customer', ?, 'debt_add', ?,
                                             ?, ?, ?, ?,
                                             'sale', ?, ?, ?, NOW())";
                        
                        $trans_stmt = $conn->prepare($transaction_query);
                        $description = "تسجيل دين من مبيعة رقم: " . $sale['sale_number'];
                        $user_id = $_SESSION['user_id'] ?? 1;
                        
                        $trans_stmt->bind_param("idddddiis", 
                            $customer_id, $remaining_amount,
                            $wallet_before, $wallet_before,
                            $debt_before, $balance_result['debt_balance'],
                            $sale_id, $description, $user_id
                        );
                        $trans_stmt->execute();
                    }
                } else {
                    // استخدام النظام القديم
                    $update_balance_query = "UPDATE customers SET balance = balance + ? WHERE id = ?";
                    $stmt = $conn->prepare($update_balance_query);
                    $stmt->bind_param("di", $remaining_amount, $customer_id);
                    $stmt->execute();
                }
                
                // تحديث المبيعة لتسجيل أن الدين تم تسجيله
                $update_sale_query = "UPDATE sales SET debt_amount = ? WHERE id = ?";
                $sale_stmt = $conn->prepare($update_sale_query);
                $sale_stmt->bind_param("di", $remaining_amount, $sale_id);
                $sale_stmt->execute();
                
                $fixes_applied[] = "تم تسجيل دين {$remaining_amount} ريال للعميل: {$customer_name} من مبيعة رقم: {$sale['sale_number']}";
                $total_fixed++;
            }
        }
        
        if ($total_fixed > 0) {
            $fixes_applied[] = "تم إصلاح {$total_fixed} مبيعة وتسجيل الديون المفقودة";
        } else {
            $fixes_applied[] = "لا توجد ديون مفقودة تحتاج إصلاح";
        }
        
        $conn->commit();
        
    } catch (Exception $e) {
        $conn->rollback();
        $errors[] = "خطأ في الإصلاح: " . $e->getMessage();
    }
}

// فحص حالة النظام
$system_status = [];

try {
    // فحص وجود الأعمدة المتقدمة
    $check_advanced = $conn->query("SHOW COLUMNS FROM customers LIKE 'debt_balance'");
    $system_status['has_advanced_system'] = $check_advanced->num_rows > 0;
    
    // فحص العملاء المذكورين
    $customers_query = "SELECT id, name, balance, 
                       COALESCE(wallet_balance, 0) as wallet_balance,
                       COALESCE(debt_balance, 0) as debt_balance
                       FROM customers 
                       WHERE name LIKE '%خالد%' OR name LIKE '%فاطمة%'
                       ORDER BY name";
    $customers_result = $conn->query($customers_query);
    $system_status['mentioned_customers'] = [];
    
    while ($customer = $customers_result->fetch_assoc()) {
        $system_status['mentioned_customers'][] = $customer;
    }
    
    // فحص المبيعات التي لها مبالغ متبقية
    $pending_debts_query = "
        SELECT s.id, s.sale_number, s.customer_id, s.final_amount, s.paid_amount, 
               (s.final_amount - s.paid_amount) as remaining_amount,
               c.name as customer_name, s.sale_date
        FROM sales s 
        LEFT JOIN customers c ON s.customer_id = c.id 
        WHERE s.customer_id > 1 
        AND (s.final_amount - s.paid_amount) > 0.01
        AND s.customer_id IS NOT NULL
        ORDER BY s.created_at DESC
        LIMIT 10
    ";
    
    $pending_result = $conn->query($pending_debts_query);
    $system_status['pending_debts'] = [];
    
    while ($debt = $pending_result->fetch_assoc()) {
        $system_status['pending_debts'][] = $debt;
    }
    
    // إحصائيات عامة
    $stats_query = "SELECT 
                    COUNT(*) as total_customers,
                    SUM(CASE WHEN balance > 0 THEN 1 ELSE 0 END) as customers_with_balance,
                    SUM(balance) as total_old_balance,
                    SUM(COALESCE(debt_balance, 0)) as total_debt_balance
                    FROM customers";
    $stats_result = $conn->query($stats_query);
    $system_status['stats'] = $stats_result->fetch_assoc();
    
} catch (Exception $e) {
    $errors[] = "خطأ في فحص النظام: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .status-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        .status-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .status-danger {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .customer-card {
            background: #e8f4fd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #0066cc;
        }
        .debt-item {
            background: #fff3cd;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-balance-scale me-3"></i>
                إصلاح أرصدة العملاء
            </h1>
            <p class="lead">تسجيل الديون المفقودة وإصلاح أرصدة العملاء</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($fixes_applied)): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>تم تطبيق الإصلاحات:</h5>
                <ul class="mb-0">
                    <?php foreach ($fixes_applied as $fix): ?>
                        <li><?php echo htmlspecialchars($fix); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle me-2"></i>أخطاء:</h5>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- حالة النظام -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-chart-line me-2"></i>حالة النظام</h3>
            </div>
            <div class="card-body">
                
                <div class="status-item <?php echo $system_status['has_advanced_system'] ? 'status-success' : 'status-warning'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $system_status['has_advanced_system'] ? 'check' : 'exclamation-triangle'; ?> me-2"></i>
                        نظام الأرصدة المتقدم
                    </h6>
                    <p class="mb-0">
                        <?php if ($system_status['has_advanced_system']): ?>
                            مفعل - يدعم الأرصدة المحفوظة والديون المنفصلة
                        <?php else: ?>
                            غير مفعل - يستخدم النظام القديم (عمود balance فقط)
                        <?php endif; ?>
                    </p>
                </div>
                
                <!-- إحصائيات -->
                <div class="row mt-3">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary"><?php echo $system_status['stats']['total_customers']; ?></h4>
                            <small>إجمالي العملاء</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning"><?php echo $system_status['stats']['customers_with_balance']; ?></h4>
                            <small>عملاء لهم أرصدة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info"><?php echo number_format($system_status['stats']['total_old_balance'], 2); ?></h4>
                            <small>إجمالي الرصيد القديم</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-danger"><?php echo number_format($system_status['stats']['total_debt_balance'], 2); ?></h4>
                            <small>إجمالي الديون الجديدة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- العملاء المذكورين -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-users me-2"></i>العملاء المذكورين (خالد وفاطمة)</h3>
            </div>
            <div class="card-body">
                
                <?php if (!empty($system_status['mentioned_customers'])): ?>
                    <?php foreach ($system_status['mentioned_customers'] as $customer): ?>
                        <div class="customer-card">
                            <h6><i class="fas fa-user me-2"></i><?php echo htmlspecialchars($customer['name']); ?></h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <small><strong>الرصيد القديم:</strong> <?php echo number_format($customer['balance'], 2); ?> ريال</small>
                                </div>
                                <div class="col-md-4">
                                    <small><strong>الرصيد المحفوظ:</strong> <?php echo number_format($customer['wallet_balance'], 2); ?> ريال</small>
                                </div>
                                <div class="col-md-4">
                                    <small><strong>رصيد الديون:</strong> <?php echo number_format($customer['debt_balance'], 2); ?> ريال</small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        لم يتم العثور على عملاء بأسماء "خالد" أو "فاطمة"
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- الديون المعلقة -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-exclamation-triangle me-2"></i>المبيعات التي لها مبالغ متبقية</h3>
            </div>
            <div class="card-body">
                
                <?php if (!empty($system_status['pending_debts'])): ?>
                    <p class="text-muted">هذه المبيعات لها مبالغ متبقية قد تحتاج تسجيل كديون:</p>
                    
                    <?php foreach ($system_status['pending_debts'] as $debt): ?>
                        <div class="debt-item">
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <strong>مبيعة رقم: <?php echo htmlspecialchars($debt['sale_number']); ?></strong>
                                </div>
                                <div class="col-md-3">
                                    <small>العميل: <?php echo htmlspecialchars($debt['customer_name']); ?></small>
                                </div>
                                <div class="col-md-2">
                                    <small>الإجمالي: <?php echo number_format($debt['final_amount'], 2); ?></small>
                                </div>
                                <div class="col-md-2">
                                    <small>المدفوع: <?php echo number_format($debt['paid_amount'], 2); ?></small>
                                </div>
                                <div class="col-md-2">
                                    <strong class="text-danger">المتبقي: <?php echo number_format($debt['remaining_amount'], 2); ?></strong>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        يمكنك إصلاح هذه المبيعات وتسجيل الديون المفقودة باستخدام الزر أدناه
                    </div>
                    
                <?php else: ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        جميع المبيعات مدفوعة بالكامل أو مسجلة كديون
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- إجراء الإصلاح -->
        <?php if (!empty($system_status['pending_debts'])): ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-tools me-2"></i>إصلاح الأرصدة</h3>
                </div>
                <div class="card-body">
                    <p>سيتم تطبيق الإصلاحات التالية:</p>
                    <ul>
                        <li>تسجيل المبالغ المتبقية كديون للعملاء</li>
                        <li>تحديث أرصدة العملاء في النظام</li>
                        <li>تسجيل معاملات الرصيد في السجلات</li>
                        <li>ربط الديون بالمبيعات الأصلية</li>
                    </ul>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من تطبيق إصلاحات الأرصدة؟')">
                        <button type="submit" name="fix_balances" class="btn btn-success btn-lg">
                            <i class="fas fa-magic me-2"></i>
                            إصلاح الأرصدة الآن
                        </button>
                    </form>
                </div>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-check-circle me-2"></i>النظام سليم!</h3>
                </div>
                <div class="card-body">
                    <p>جميع الأرصدة مسجلة بشكل صحيح. لا توجد ديون مفقودة تحتاج إصلاح.</p>
                    
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="pages/customers/index.php" class="btn btn-primary btn-lg me-md-2">
                            <i class="fas fa-users me-2"></i>
                            مراجعة العملاء
                        </a>
                        <a href="pages/sales/index.php" class="btn btn-info btn-lg">
                            <i class="fas fa-receipt me-2"></i>
                            مراجعة المبيعات
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <h2>⚖️ إصلاح أرصدة العملاء! ⚖️</h2>
            <p class="lead">تسجيل الديون المفقودة وضمان دقة الأرصدة</p>
            
            <a href="index.php" class="btn btn-light btn-lg mt-3">
                <i class="fas fa-home me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
