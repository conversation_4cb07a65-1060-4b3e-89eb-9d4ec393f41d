<?php
/**
 * صفحة إضافة عميل جديد
 * Add New Customer Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "إضافة عميل جديد";
$page_icon = "fas fa-user-plus";

// معالجة إضافة العميل
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_customer'])) {
    try {
        // بيانات العميل
        $name = clean($conn, $_POST['name']);
        $phone = clean($conn, $_POST['phone']);
        $email = clean($conn, $_POST['email']);
        $address = clean($conn, $_POST['address']);
        $balance = floatval($_POST['balance']);
        $notes = clean($conn, $_POST['notes']);
        
        // التحقق من صحة البيانات
        if (empty($name)) {
            throw new Exception("اسم العميل مطلوب");
        }
        
        // التحقق من عدم تكرار الهاتف أو البريد الإلكتروني
        if (!empty($phone)) {
            $check_phone = $conn->prepare("SELECT id FROM customers WHERE phone = ? AND id != 0");
            $check_phone->bind_param("s", $phone);
            $check_phone->execute();
            if ($check_phone->get_result()->num_rows > 0) {
                throw new Exception("رقم الهاتف موجود مسبقاً");
            }
        }
        
        if (!empty($email)) {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception("البريد الإلكتروني غير صحيح");
            }
            
            $check_email = $conn->prepare("SELECT id FROM customers WHERE email = ? AND id != 0");
            $check_email->bind_param("s", $email);
            $check_email->execute();
            if ($check_email->get_result()->num_rows > 0) {
                throw new Exception("البريد الإلكتروني موجود مسبقاً");
            }
        }
        
        // التحقق من وجود عمود الملاحظات
        $notes_query = "SHOW COLUMNS FROM customers LIKE 'notes'";
        $has_notes = $conn->query($notes_query)->num_rows > 0;

        // إدراج العميل بناءً على الأعمدة المتاحة
        if ($has_notes) {
            $insert_query = "INSERT INTO customers (name, phone, email, address, balance, notes)
                            VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param("ssssds", $name, $phone, $email, $address, $balance, $notes);
        } else {
            $insert_query = "INSERT INTO customers (name, phone, email, address, balance)
                            VALUES (?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param("ssssd", $name, $phone, $email, $address, $balance);
        }
        $stmt->execute();
        
        $_SESSION['success_message'] = "تم إضافة العميل بنجاح";
        header("Location: index.php");
        exit();
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = "خطأ في إضافة العميل: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .form-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        .required {
            color: #dc3545;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">إضافة عميل جديد إلى النظام</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="index.php" class="btn btn-light btn-lg">
                        <i class="fas fa-list me-2"></i>
                        قائمة العملاء
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-container">
                    <h4 class="mb-4">
                        <i class="fas fa-user me-2 text-info"></i>
                        بيانات العميل
                    </h4>
                    
                    <form method="POST" id="customerForm">
                        <div class="row">
                            
                            <!-- المعلومات الأساسية -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم العميل <span class="required">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="balance" class="form-label">الرصيد الابتدائي</label>
                                <input type="number" class="form-control" id="balance" name="balance" 
                                       step="0.01" value="0">
                                <small class="text-muted">الرصيد الموجب يعني أن العميل له رصيد دائن، والسالب يعني عليه دين</small>
                            </div>
                            
                            <div class="col-md-12 mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                            </div>
                            
                            <div class="col-md-12 mb-4">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                            </div>
                            
                            <!-- أزرار الحفظ -->
                            <div class="col-md-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="index.php" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                    <button type="submit" name="add_customer" class="btn btn-info">
                                        <i class="fas fa-save me-2"></i>حفظ العميل
                                    </button>
                                </div>
                            </div>
                            
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // التحقق من صحة النموذج
        document.getElementById('customerForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            
            if (!name) {
                e.preventDefault();
                alert('اسم العميل مطلوب');
                return false;
            }
            
            if (email && !isValidEmail(email)) {
                e.preventDefault();
                alert('البريد الإلكتروني غير صحيح');
                return false;
            }
        });
        
        // دالة التحقق من صحة البريد الإلكتروني
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        // تنسيق رقم الهاتف
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // إزالة كل شيء عدا الأرقام
            
            // تنسيق الرقم (اختياري)
            if (value.length > 0) {
                if (value.length <= 3) {
                    value = value;
                } else if (value.length <= 6) {
                    value = value.slice(0, 3) + '-' + value.slice(3);
                } else if (value.length <= 10) {
                    value = value.slice(0, 3) + '-' + value.slice(3, 6) + '-' + value.slice(6);
                } else {
                    value = value.slice(0, 3) + '-' + value.slice(3, 6) + '-' + value.slice(6, 10);
                }
            }
            
            e.target.value = value;
        });
    </script>

</body>
</html>
