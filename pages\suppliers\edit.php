<?php
// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/config.php";
require_once "../../config/database.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
session_start();
if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "تعديل بيانات المورد";
$page_icon = "fas fa-edit";
$base_url = "../../";

// التحقق من وجود معرف المورد
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error_message'] = "معرف المورد غير صحيح";
    header("Location: index.php");
    exit();
}

$supplier_id = intval($_GET['id']);

// الحصول على بيانات المورد
$query = "SELECT * FROM suppliers WHERE id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $supplier_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    $_SESSION['error_message'] = "المورد غير موجود";
    header("Location: index.php");
    exit();
}

$supplier = $result->fetch_assoc();

// معالجة تحديث المورد
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_supplier'])) {
    try {
        // تنظيف البيانات
        $name = clean($conn, $_POST['name']);
        $company = clean($conn, $_POST['company']);
        $phone = clean($conn, $_POST['phone']);
        $email = clean($conn, $_POST['email']);
        $address = clean($conn, $_POST['address']);
        $balance = floatval($_POST['balance']);
        $notes = clean($conn, $_POST['notes']);
        
        // التحقق من صحة البيانات
        if (empty($name)) {
            throw new Exception("اسم المورد مطلوب");
        }
        
        // التحقق من عدم تكرار اسم المورد (باستثناء المورد الحالي)
        $check_query = "SELECT id FROM suppliers WHERE name = ? AND id != ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("si", $name, $supplier_id);
        $check_stmt->execute();
        $existing = $check_stmt->get_result();
        
        if ($existing->num_rows > 0) {
            throw new Exception("يوجد مورد آخر بنفس الاسم");
        }
        
        // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
        if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception("البريد الإلكتروني غير صحيح");
        }
        
        // التحقق من وجود الأعمدة
        $columns_query = "SHOW COLUMNS FROM suppliers LIKE 'company'";
        $has_company = $conn->query($columns_query)->num_rows > 0;

        $notes_query = "SHOW COLUMNS FROM suppliers LIKE 'notes'";
        $has_notes = $conn->query($notes_query)->num_rows > 0;

        // تحديث المورد بناءً على الأعمدة المتاحة
        if ($has_company && $has_notes) {
            $update_query = "UPDATE suppliers SET name = ?, company = ?, phone = ?, email = ?, address = ?, balance = ?, notes = ? WHERE id = ?";
            $stmt = $conn->prepare($update_query);
            $stmt->bind_param("sssssdsi", $name, $company, $phone, $email, $address, $balance, $notes, $supplier_id);
        } elseif ($has_company && !$has_notes) {
            $update_query = "UPDATE suppliers SET name = ?, company = ?, phone = ?, email = ?, address = ?, balance = ? WHERE id = ?";
            $stmt = $conn->prepare($update_query);
            $stmt->bind_param("sssssdi", $name, $company, $phone, $email, $address, $balance, $supplier_id);
        } elseif (!$has_company && $has_notes) {
            $update_query = "UPDATE suppliers SET name = ?, phone = ?, email = ?, address = ?, balance = ?, notes = ? WHERE id = ?";
            $stmt = $conn->prepare($update_query);
            $stmt->bind_param("ssssdsi", $name, $phone, $email, $address, $balance, $notes, $supplier_id);
        } else {
            $update_query = "UPDATE suppliers SET name = ?, phone = ?, email = ?, address = ?, balance = ? WHERE id = ?";
            $stmt = $conn->prepare($update_query);
            $stmt->bind_param("ssssdi", $name, $phone, $email, $address, $balance, $supplier_id);
        }
        
        $stmt->execute();
        
        // إضافة حركة مالية في الخزينة إذا تغير الرصيد
        $old_balance = floatval($supplier['balance']);
        $balance_diff = $balance - $old_balance;
        
        if ($balance_diff != 0) {
            $description = "تعديل رصيد المورد: " . $name . " (الفرق: " . $balance_diff . ")";
            $type = ($balance_diff > 0) ? 'supplier_debt' : 'supplier_payment';
            addTreasuryTransaction($type, $supplier_id, abs($balance_diff), $description, $_SESSION['user_id']);
        }
        
        $_SESSION['success_message'] = "تم تحديث بيانات المورد بنجاح";
        header("Location: view.php?id=" . $supplier_id);
        exit();
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = "خطأ في تحديث المورد: " . $e->getMessage();
    }
}

// التحقق من وجود عمود company لعرض الحقل
$columns_query = "SHOW COLUMNS FROM suppliers LIKE 'company'";
$has_company_column = $conn->query($columns_query)->num_rows > 0;
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> | نظام Zero لإدارة المحلات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- نمط الخط العربي -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 0;
            margin-bottom: 2rem;
            color: white;
        }
        
        .form-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .form-header {
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .form-body {
            padding: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #ffc107;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }
        
        .btn {
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            border: none;
            color: white;
        }
        
        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 193, 7, 0.3);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
        }
        
        .required {
            color: #dc3545;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">تعديل بيانات المورد: <?php echo htmlspecialchars($supplier['name']); ?></p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="view.php?id=<?php echo $supplier['id']; ?>" class="btn btn-light btn-lg">
                        <i class="fas fa-eye me-2"></i>
                        عرض التفاصيل
                    </a>
                    <a href="index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-list me-2"></i>
                        قائمة الموردين
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- عرض الرسائل -->
        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            </div>
        <?php endif; ?>

        <!-- نموذج تعديل المورد -->
        <div class="form-container">
            <div class="form-header">
                <h3 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل بيانات المورد
                </h3>
            </div>
            
            <div class="form-body">
                <form method="post" action="edit.php?id=<?php echo $supplier['id']; ?>" id="supplierForm">
                    <div class="row">
                        
                        <!-- اسم المورد -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name" class="form-label">
                                    اسم المورد <span class="required">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo htmlspecialchars($supplier['name']); ?>" 
                                       placeholder="أدخل اسم المورد" required>
                            </div>
                        </div>
                        
                        <!-- اسم الشركة -->
                        <?php if ($has_company_column): ?>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="company" class="form-label">اسم الشركة</label>
                                <input type="text" class="form-control" id="company" name="company" 
                                       value="<?php echo htmlspecialchars($supplier['company'] ?? ''); ?>" 
                                       placeholder="أدخل اسم الشركة (اختياري)">
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- رقم الهاتف -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($supplier['phone'] ?? ''); ?>" 
                                       placeholder="أدخل رقم الهاتف">
                            </div>
                        </div>
                        
                        <!-- البريد الإلكتروني -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($supplier['email'] ?? ''); ?>" 
                                       placeholder="أدخل البريد الإلكتروني">
                            </div>
                        </div>
                        
                        <!-- العنوان -->
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="3" 
                                          placeholder="أدخل عنوان المورد"><?php echo htmlspecialchars($supplier['address'] ?? ''); ?></textarea>
                            </div>
                        </div>
                        
                        <!-- الرصيد -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="balance" class="form-label">الرصيد الحالي</label>
                                <input type="number" class="form-control" id="balance" name="balance" 
                                       step="0.01" value="<?php echo $supplier['balance']; ?>" placeholder="0.00">
                                <small class="form-text text-muted">
                                    الرصيد الموجب يعني أن المورد مدين لك، والرصيد السالب يعني أنك مدين للمورد
                                </small>
                            </div>
                        </div>
                        
                        <!-- ملاحظات -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                          placeholder="أدخل أي ملاحظات إضافية"><?php echo htmlspecialchars($supplier['notes'] ?? ''); ?></textarea>
                            </div>
                        </div>
                        
                        <!-- أزرار الحفظ -->
                        <div class="col-md-12">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="view.php?id=<?php echo $supplier['id']; ?>" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                                <button type="submit" name="update_supplier" class="btn btn-warning">
                                    <i class="fas fa-save me-2"></i>حفظ التعديلات
                                </button>
                            </div>
                        </div>
                        
                    </div>
                </form>
            </div>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // التحقق من صحة النموذج
        document.getElementById('supplierForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            
            if (!name) {
                e.preventDefault();
                alert('اسم المورد مطلوب');
                return false;
            }
            
            if (email && !isValidEmail(email)) {
                e.preventDefault();
                alert('البريد الإلكتروني غير صحيح');
                return false;
            }
        });
        
        // دالة للتحقق من صحة البريد الإلكتروني
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        // تنسيق رقم الهاتف
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            e.target.value = value;
        });
        
        // تنسيق الرصيد
        document.getElementById('balance').addEventListener('input', function(e) {
            let value = parseFloat(e.target.value) || 0;
            e.target.value = value.toFixed(2);
        });
    </script>

</body>
</html>
