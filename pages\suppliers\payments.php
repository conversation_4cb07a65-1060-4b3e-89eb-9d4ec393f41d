<?php
// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/config.php";
require_once "../../config/database.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
session_start();
if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "إدارة حسابات الموردين";
$page_icon = "fas fa-money-bill-wave";
$base_url = "../../";

// التحقق من وجود معرف المورد
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error_message'] = "معرف المورد غير صحيح";
    header("Location: index.php");
    exit();
}

$supplier_id = intval($_GET['id']);

// الحصول على بيانات المورد
$query = "SELECT * FROM suppliers WHERE id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $supplier_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    $_SESSION['error_message'] = "المورد غير موجود";
    header("Location: index.php");
    exit();
}

$supplier = $result->fetch_assoc();

// معالجة دفع المستحقات
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['make_payment'])) {
    try {
        $amount = floatval($_POST['amount']);
        $payment_type = clean($conn, $_POST['payment_type']);
        $description = clean($conn, $_POST['description']);
        $payment_date = clean($conn, $_POST['payment_date']);
        
        if ($amount <= 0) {
            throw new Exception("المبلغ يجب أن يكون أكبر من صفر");
        }
        
        if (empty($description)) {
            $description = "دفعة للمورد: " . $supplier['name'];
        }
        
        // بدء المعاملة
        $conn->begin_transaction();
        
        // تحديث رصيد المورد
        if ($payment_type == 'pay_debt') {
            // دفع مستحقات (تقليل الرصيد)
            $update_query = "UPDATE suppliers SET balance = balance - ? WHERE id = ?";
            $treasury_type = 'supplier_payment';
            $treasury_description = "دفع مستحقات للمورد: " . $supplier['name'] . " - " . $description;
        } else {
            // إضافة رصيد (زيادة الرصيد)
            $update_query = "UPDATE suppliers SET balance = balance + ? WHERE id = ?";
            $treasury_type = 'supplier_debt';
            $treasury_description = "إضافة رصيد للمورد: " . $supplier['name'] . " - " . $description;
        }
        
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("di", $amount, $supplier_id);
        $stmt->execute();
        
        // إضافة حركة في الخزينة
        addTreasuryTransaction($treasury_type, $supplier_id, $amount, $treasury_description, $_SESSION['user_id'], $payment_date);
        
        // تسجيل الحركة في جدول حركات الموردين (إذا كان موجود)
        $movement_query = "INSERT INTO supplier_movements (supplier_id, type, amount, description, user_id, movement_date) 
                          VALUES (?, ?, ?, ?, ?, ?)";
        try {
            $stmt = $conn->prepare($movement_query);
            $stmt->bind_param("isdsss", $supplier_id, $payment_type, $amount, $description, $_SESSION['user_id'], $payment_date);
            $stmt->execute();
        } catch (Exception $e) {
            // تجاهل الخطأ إذا كان الجدول غير موجود
        }
        
        $conn->commit();
        $_SESSION['success_message'] = "تم تسجيل العملية بنجاح";
        header("Location: payments.php?id=" . $supplier_id);
        exit();
        
    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['error_message'] = "خطأ في تسجيل العملية: " . $e->getMessage();
    }
}

// الحصول على حركات الخزينة المتعلقة بالمورد
// التحقق من وجود عمود reference_type أولاً
$check_column = $conn->query("SHOW COLUMNS FROM treasury LIKE 'reference_type'");
$has_reference_type = $check_column->num_rows > 0;

if ($has_reference_type) {
    // استخدام العمود الجديد reference_type
    $movements_query = "SELECT * FROM treasury
                       WHERE reference_type IN ('supplier_payment', 'supplier_debt')
                       AND reference_id = ?
                       ORDER BY id DESC LIMIT 20";
} else {
    // استخدام العمود القديم transaction_type مع البحث في الوصف
    $movements_query = "SELECT * FROM treasury
                       WHERE reference_id = ?
                       AND (description LIKE '%مورد%' OR description LIKE '%supplier%')
                       ORDER BY id DESC LIMIT 20";
}

$stmt = $conn->prepare($movements_query);
$stmt->bind_param("i", $supplier_id);
$stmt->execute();
$movements = $stmt->get_result();
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> | نظام Zero لإدارة المحلات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- نمط الخط العربي -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 0;
            margin-bottom: 2rem;
            color: white;
        }
        
        .payment-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .card-body {
            padding: 2rem;
        }
        
        .balance-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .balance-amount {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .balance-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        
        .btn {
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .movements-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .movement-in {
            color: #28a745;
            font-weight: bold;
        }
        
        .movement-out {
            color: #dc3545;
            font-weight: bold;
        }
        
        .required {
            color: #dc3545;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">إدارة حساب المورد: <?php echo htmlspecialchars($supplier['name']); ?></p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="view.php?id=<?php echo $supplier['id']; ?>" class="btn btn-light btn-lg">
                        <i class="fas fa-eye me-2"></i>
                        عرض التفاصيل
                    </a>
                    <a href="index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-list me-2"></i>
                        قائمة الموردين
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- عرض الرسائل -->
        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- معلومات الرصيد -->
            <div class="col-md-4">
                <div class="balance-info">
                    <div class="balance-amount">
                        <?php echo formatMoney($supplier['balance'], false); ?>
                    </div>
                    <div class="balance-label">الرصيد الحالي</div>
                    <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                    <div class="text-center">
                        <?php if ($supplier['balance'] > 0): ?>
                            <i class="fas fa-arrow-up me-2"></i>
                            <span>مستحقات لنا</span>
                        <?php elseif ($supplier['balance'] < 0): ?>
                            <i class="fas fa-arrow-down me-2"></i>
                            <span>مستحقات علينا</span>
                        <?php else: ?>
                            <i class="fas fa-equals me-2"></i>
                            <span>رصيد متوازن</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- نموذج العمليات المالية -->
            <div class="col-md-8">
                <div class="payment-card">
                    <div class="card-header">
                        <h3 class="mb-0">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            تسجيل عملية مالية
                        </h3>
                    </div>
                    
                    <div class="card-body">
                        <form method="post" action="payments.php?id=<?php echo $supplier['id']; ?>" id="paymentForm">
                            <div class="row">
                                
                                <!-- نوع العملية -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="payment_type" class="form-label">
                                            نوع العملية <span class="required">*</span>
                                        </label>
                                        <select class="form-select" id="payment_type" name="payment_type" required>
                                            <option value="">اختر نوع العملية</option>
                                            <option value="pay_debt">دفع مستحقات (تقليل الرصيد)</option>
                                            <option value="add_debt">إضافة رصيد (زيادة الرصيد)</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <!-- المبلغ -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="amount" class="form-label">
                                            المبلغ <span class="required">*</span>
                                        </label>
                                        <input type="number" class="form-control" id="amount" name="amount" 
                                               step="0.01" min="0.01" placeholder="0.00" required>
                                    </div>
                                </div>
                                
                                <!-- تاريخ العملية -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="payment_date" class="form-label">
                                            تاريخ العملية <span class="required">*</span>
                                        </label>
                                        <input type="date" class="form-control" id="payment_date" name="payment_date" 
                                               value="<?php echo date('Y-m-d'); ?>" required>
                                    </div>
                                </div>
                                
                                <!-- الوصف -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="description" class="form-label">الوصف</label>
                                        <input type="text" class="form-control" id="description" name="description" 
                                               placeholder="وصف العملية (اختياري)">
                                    </div>
                                </div>
                                
                                <!-- أزرار الحفظ -->
                                <div class="col-md-12">
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="view.php?id=<?php echo $supplier['id']; ?>" class="btn btn-secondary me-md-2">
                                            <i class="fas fa-times me-2"></i>إلغاء
                                        </a>
                                        <button type="submit" name="make_payment" class="btn btn-success">
                                            <i class="fas fa-save me-2"></i>تسجيل العملية
                                        </button>
                                    </div>
                                </div>
                                
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- آخر الحركات المالية -->
        <?php if ($movements->num_rows > 0): ?>
        <div class="movements-table">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر الحركات المالية
                </h5>
            </div>
            
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>التاريخ</th>
                            <th>نوع الحركة</th>
                            <th>المبلغ</th>
                            <th>الرصيد بعد العملية</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($movement = $movements->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo formatDate($movement['transaction_date']); ?></td>
                            <td>
                                <?php
                                // تحديد نوع الحركة من الوصف أو transaction_type
                                $is_payment = false;
                                if (isset($movement['reference_type'])) {
                                    $is_payment = ($movement['reference_type'] == 'supplier_payment');
                                } else {
                                    // تحديد النوع من الوصف
                                    $desc = strtolower($movement['description']);
                                    $is_payment = (strpos($desc, 'دفع') !== false || strpos($desc, 'payment') !== false);
                                }
                                ?>
                                <?php if ($is_payment): ?>
                                    <span class="badge bg-danger">دفع مستحقات</span>
                                <?php else: ?>
                                    <span class="badge bg-success">إضافة رصيد</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($is_payment): ?>
                                    <span class="movement-out">-<?php echo formatMoney($movement['amount']); ?></span>
                                <?php else: ?>
                                    <span class="movement-in">+<?php echo formatMoney($movement['amount']); ?></span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo formatMoney($movement['balance_after']); ?></td>
                            <td><?php echo htmlspecialchars($movement['description']); ?></td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php endif; ?>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // التحقق من صحة النموذج
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            const amount = parseFloat(document.getElementById('amount').value) || 0;
            const paymentType = document.getElementById('payment_type').value;
            
            if (amount <= 0) {
                e.preventDefault();
                alert('المبلغ يجب أن يكون أكبر من صفر');
                return false;
            }
            
            if (!paymentType) {
                e.preventDefault();
                alert('يرجى اختيار نوع العملية');
                return false;
            }
            
            // تأكيد العملية
            const currentBalance = <?php echo $supplier['balance']; ?>;
            let newBalance = currentBalance;
            
            if (paymentType === 'pay_debt') {
                newBalance = currentBalance - amount;
            } else {
                newBalance = currentBalance + amount;
            }
            
            const confirmMessage = `هل أنت متأكد من تسجيل هذه العملية؟\n` +
                                 `المبلغ: ${amount.toFixed(2)}\n` +
                                 `الرصيد الحالي: ${currentBalance.toFixed(2)}\n` +
                                 `الرصيد بعد العملية: ${newBalance.toFixed(2)}`;
            
            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return false;
            }
        });
        
        // تنسيق المبلغ
        document.getElementById('amount').addEventListener('input', function(e) {
            let value = parseFloat(e.target.value) || 0;
            if (value < 0) {
                e.target.value = 0;
            }
        });
    </script>

</body>
</html>
