<?php
/**
 * صفحة إدارة الموردين الرئيسية
 * Suppliers Management Main Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "إدارة الموردين";
$page_icon = "fas fa-truck";

// معالجة حذف المورد
if (isset($_POST['delete_supplier'])) {
    $supplier_id = clean($conn, $_POST['supplier_id']);
    
    try {
        // التحقق من وجود مشتريات للمورد
        $check_purchases = $conn->query("SELECT COUNT(*) as count FROM purchases WHERE supplier_id = $supplier_id")->fetch_assoc()['count'];
        
        if ($check_purchases > 0) {
            $_SESSION['error_message'] = "لا يمكن حذف المورد لوجود مشتريات مرتبطة به";
        } else {
            // حذف المورد
            $delete_query = "DELETE FROM suppliers WHERE id = ?";
            $stmt = $conn->prepare($delete_query);
            $stmt->bind_param("i", $supplier_id);
            $stmt->execute();
            $_SESSION['success_message'] = "تم حذف المورد بنجاح";
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = "خطأ في حذف المورد: " . $e->getMessage();
    }
    
    header("Location: index.php");
    exit();
}

// الحصول على الموردين مع الفلترة والبحث
$search = isset($_GET['search']) ? clean($conn, $_GET['search']) : '';
$balance_status = isset($_GET['balance_status']) ? clean($conn, $_GET['balance_status']) : '';

$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR company LIKE ? OR phone LIKE ? OR email LIKE ? OR address LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= 'sssss';
}

if (!empty($balance_status)) {
    if ($balance_status == 'positive') {
        $where_conditions[] = "balance > 0";
    } elseif ($balance_status == 'negative') {
        $where_conditions[] = "balance < 0";
    } elseif ($balance_status == 'zero') {
        $where_conditions[] = "balance = 0";
    }
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
}

// استعلام الموردين
$query = "SELECT * FROM suppliers $where_clause ORDER BY name ASC";

if (!empty($params)) {
    $stmt = $conn->prepare($query);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $suppliers_result = $stmt->get_result();
} else {
    $suppliers_result = $conn->query($query);
}

// حساب الإحصائيات
$stats_query = "SELECT 
                COUNT(*) as total_suppliers,
                COUNT(CASE WHEN balance > 0 THEN 1 END) as suppliers_with_credit,
                COUNT(CASE WHEN balance < 0 THEN 1 END) as suppliers_with_debt,
                COALESCE(SUM(CASE WHEN balance > 0 THEN balance ELSE 0 END), 0) as total_credit,
                COALESCE(SUM(CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END), 0) as total_debt
                FROM suppliers $where_clause";

if (!empty($params)) {
    $stmt = $conn->prepare($stats_query);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $stats = $stmt->get_result()->fetch_assoc();
} else {
    $stats = $conn->query($stats_query)->fetch_assoc();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #fd7e14 0%, #e55a4e 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .btn-action {
            padding: 0.25rem 0.5rem;
            margin: 0 0.1rem;
            border-radius: 0.375rem;
        }
        .search-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .balance-positive {
            color: #28a745;
            font-weight: bold;
        }
        .balance-negative {
            color: #dc3545;
            font-weight: bold;
        }
        .balance-zero {
            color: #6c757d;
            font-weight: bold;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">إدارة ومتابعة جميع الموردين والمستحقات</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="add.php" class="btn btn-light btn-lg">
                        <i class="fas fa-plus me-2"></i>
                        مورد جديد
                    </a>
                    <a href="reports.php" class="btn btn-info btn-lg ms-2">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-warning mb-2">
                            <i class="fas fa-truck fa-2x"></i>
                        </div>
                        <h3 class="card-title text-warning"><?php echo number_format($stats['total_suppliers']); ?></h3>
                        <p class="card-text text-muted">إجمالي الموردين</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-success mb-2">
                            <i class="fas fa-arrow-down fa-2x"></i>
                        </div>
                        <h3 class="card-title text-success"><?php echo formatMoney($stats['total_credit']); ?></h3>
                        <p class="card-text text-muted">إجمالي المستحقات لنا</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-danger mb-2">
                            <i class="fas fa-arrow-up fa-2x"></i>
                        </div>
                        <h3 class="card-title text-danger"><?php echo formatMoney($stats['total_debt']); ?></h3>
                        <p class="card-text text-muted">إجمالي المستحقات علينا</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-info mb-2">
                            <i class="fas fa-balance-scale fa-2x"></i>
                        </div>
                        <h3 class="card-title text-info"><?php echo formatMoney($stats['total_debt'] - $stats['total_credit']); ?></h3>
                        <p class="card-text text-muted">صافي المستحقات</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- البحث والفلترة -->
        <div class="search-container">
            <form method="GET" class="row g-3">
                <div class="col-md-6">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="الاسم، الشركة، الهاتف، البريد الإلكتروني، أو العنوان">
                </div>
                <div class="col-md-4">
                    <label for="balance_status" class="form-label">حالة الرصيد</label>
                    <select class="form-select" id="balance_status" name="balance_status">
                        <option value="">جميع الأرصدة</option>
                        <option value="positive" <?php echo ($balance_status == 'positive') ? 'selected' : ''; ?>>مستحقات لنا</option>
                        <option value="negative" <?php echo ($balance_status == 'negative') ? 'selected' : ''; ?>>مستحقات علينا</option>
                        <option value="zero" <?php echo ($balance_status == 'zero') ? 'selected' : ''; ?>>رصيد صفر</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                    </div>
                </div>
            </form>
            
            <?php if (!empty($search) || !empty($balance_status)): ?>
                <div class="mt-3">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> مسح الفلاتر
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- جدول الموردين -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>اسم المورد</th>
                            <th>الشركة</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>الرصيد</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($suppliers_result->num_rows > 0): ?>
                            <?php while ($supplier = $suppliers_result->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($supplier['name']); ?></strong>
                                    </td>
                                    <td><?php echo htmlspecialchars($supplier['company'] ?? '-'); ?></td>
                                    <td><?php echo htmlspecialchars($supplier['phone'] ?? '-'); ?></td>
                                    <td><?php echo htmlspecialchars($supplier['email'] ?? '-'); ?></td>
                                    <td>
                                        <?php 
                                        $balance_class = '';
                                        if ($supplier['balance'] > 0) {
                                            $balance_class = 'balance-positive';
                                        } elseif ($supplier['balance'] < 0) {
                                            $balance_class = 'balance-negative';
                                        } else {
                                            $balance_class = 'balance-zero';
                                        }
                                        ?>
                                        <span class="<?php echo $balance_class; ?>">
                                            <?php echo formatMoney($supplier['balance']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($supplier['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="view.php?id=<?php echo $supplier['id']; ?>" 
                                               class="btn btn-info btn-action" 
                                               data-bs-toggle="tooltip" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $supplier['id']; ?>" 
                                               class="btn btn-warning btn-action" 
                                               data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-danger btn-action" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#deleteModal<?php echo $supplier['id']; ?>"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Modal حذف المورد -->
                                        <div class="modal fade" id="deleteModal<?php echo $supplier['id']; ?>" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">تأكيد الحذف</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>هل أنت متأكد من حذف المورد <strong><?php echo htmlspecialchars($supplier['name']); ?></strong>؟</p>
                                                        <p class="text-warning"><small>ملاحظة: لا يمكن حذف المورد إذا كان له مشتريات مرتبطة.</small></p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="supplier_id" value="<?php echo $supplier['id']; ?>">
                                                            <button type="submit" name="delete_supplier" class="btn btn-danger">حذف</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد موردين</h5>
                                    <p class="text-muted">لم يتم العثور على أي موردين تطابق معايير البحث</p>
                                    <a href="add.php" class="btn btn-warning">
                                        <i class="fas fa-plus me-2"></i>إضافة مورد جديد
                                    </a>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تفعيل التلميحات
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    </script>

</body>
</html>
