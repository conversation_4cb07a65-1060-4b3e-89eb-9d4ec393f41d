<?php
/**
 * صفحة تعديل المبيعة
 * Edit Sale Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

// التحقق من وجود معرف المبيعة
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error_message'] = "معرف المبيعة غير صحيح";
    header("Location: index.php");
    exit();
}

$sale_id = intval($_GET['id']);

// الحصول على بيانات المبيعة
$sale_query = "SELECT * FROM sales WHERE id = ?";
$stmt = $conn->prepare($sale_query);
$stmt->bind_param("i", $sale_id);
$stmt->execute();
$sale_result = $stmt->get_result();

if ($sale_result->num_rows === 0) {
    $_SESSION['error_message'] = "المبيعة غير موجودة";
    header("Location: index.php");
    exit();
}

$sale = $sale_result->fetch_assoc();

// معالجة تحديث المبيعة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_sale'])) {
    try {
        $conn->begin_transaction();
        
        // بيانات المبيعة المحدثة
        $customer_id = clean($conn, $_POST['customer_id']);
        $sale_date = clean($conn, $_POST['sale_date']);
        $discount_amount = floatval($_POST['discount_amount']);
        $paid_amount = floatval($_POST['paid_amount']);
        $payment_method = clean($conn, $_POST['payment_method']);
        $notes = clean($conn, $_POST['notes']);
        
        // حساب المبلغ النهائي من الأصناف الموجودة
        $items_total_query = "SELECT COALESCE(SUM(total_price), 0) as total FROM sale_items WHERE sale_id = ?";
        $stmt = $conn->prepare($items_total_query);
        $stmt->bind_param("i", $sale_id);
        $stmt->execute();
        $total_amount = $stmt->get_result()->fetch_assoc()['total'];
        
        $final_amount = $total_amount - $discount_amount;
        
        // تحديث المبيعة
        $update_query = "UPDATE sales SET customer_id = ?, sale_date = ?, total_amount = ?, discount_amount = ?, 
                        final_amount = ?, paid_amount = ?, payment_method = ?, notes = ? WHERE id = ?";
        
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("isddddss", $customer_id, $sale_date, $total_amount, $discount_amount, 
                         $final_amount, $paid_amount, $payment_method, $notes, $sale_id);
        $stmt->execute();
        
        // تحديث الخزينة
        if ($paid_amount > 0) {
            $description = "مبيعة رقم: " . $sale['sale_number'] . " (محدثة)";
            updateTreasuryTransaction('sales', $sale_id, $paid_amount, $description, $_SESSION['user_id'], $sale_date);
        } else {
            // حذف المعاملة إذا لم يعد هناك مبلغ مدفوع
            deleteTreasuryTransaction('sales', $sale_id);
        }

        // مزامنة تلقائية لرصيد العميل بعد تحديث المبيعة
        autoSyncCustomerBalance($customer_id);

        // تحديث مبلغ الدين في المبيعة الحالية
        if ($customer_id > 1) {
            $update_sale_debt = "UPDATE sales SET debt_amount = ? WHERE id = ?";
            $sale_debt_stmt = $conn->prepare($update_sale_debt);
            $current_debt = $final_amount - $paid_amount;
            $sale_debt_stmt->bind_param("di", $current_debt, $sale_id);
            $sale_debt_stmt->execute();
        }

        $conn->commit();
        $_SESSION['success_message'] = "تم تحديث المبيعة بنجاح";
        header("Location: view.php?id=$sale_id");
        exit();
        
    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['error_message'] = "خطأ في تحديث المبيعة: " . $e->getMessage();
    }
}

// الحصول على قائمة العملاء
$customers_query = "SELECT id, name, phone FROM customers ORDER BY name";
$customers_result = $conn->query($customers_query);

// الحصول على أصناف المبيعة
$items_query = "SELECT si.*, p.name as product_name, p.unit 
                FROM sale_items si 
                LEFT JOIN products p ON si.product_id = p.id 
                WHERE si.sale_id = ? 
                ORDER BY si.id";

$stmt = $conn->prepare($items_query);
$stmt->bind_param("i", $sale_id);
$stmt->execute();
$items_result = $stmt->get_result();

$page_title = "تعديل المبيعة رقم: " . $sale['sale_number'];
$page_icon = "fas fa-edit";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .form-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        .items-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
        }
        .summary-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
        }
        .select2-container--default .select2-selection--single {
            height: 38px;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 36px;
            padding-right: 12px;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        تعديل المبيعة
                    </h1>
                    <p class="mb-0 mt-2">رقم المبيعة: <?php echo htmlspecialchars($sale['sale_number']); ?></p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="view.php?id=<?php echo $sale['id']; ?>" class="btn btn-light btn-lg">
                        <i class="fas fa-eye me-2"></i>
                        عرض التفاصيل
                    </a>
                    <a href="index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-list me-2"></i>
                        قائمة المبيعات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <form method="POST" id="editSaleForm">
            <div class="row">
                
                <!-- معلومات المبيعة -->
                <div class="col-lg-8">
                    <div class="form-container mb-4">
                        <h4 class="mb-4">
                            <i class="fas fa-info-circle me-2 text-primary"></i>
                            معلومات المبيعة
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="customer_id" class="form-label">العميل <span class="text-danger">*</span></label>
                                <select class="form-select" id="customer_id" name="customer_id" required>
                                    <option value="">اختر العميل</option>
                                    <?php while ($customer = $customers_result->fetch_assoc()): ?>
                                        <option value="<?php echo $customer['id']; ?>" 
                                                <?php echo ($customer['id'] == $sale['customer_id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($customer['name']); ?>
                                            <?php if (!empty($customer['phone'])): ?>
                                                - <?php echo htmlspecialchars($customer['phone']); ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="sale_date" class="form-label">تاريخ المبيعة <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="sale_date" name="sale_date" 
                                       value="<?php echo $sale['sale_date']; ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="payment_method" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="cash" <?php echo ($sale['payment_method'] == 'cash') ? 'selected' : ''; ?>>نقدي</option>
                                    <option value="card" <?php echo ($sale['payment_method'] == 'card') ? 'selected' : ''; ?>>بطاقة</option>
                                    <option value="bank_transfer" <?php echo ($sale['payment_method'] == 'bank_transfer') ? 'selected' : ''; ?>>تحويل بنكي</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="2" 
                                          placeholder="ملاحظات إضافية (اختياري)"><?php echo htmlspecialchars($sale['notes']); ?></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- أصناف المبيعة -->
                    <div class="form-container">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4 class="mb-0">
                                <i class="fas fa-box me-2 text-primary"></i>
                                أصناف المبيعة
                            </h4>
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                لتعديل الأصناف، يرجى حذف المبيعة وإنشاء مبيعة جديدة
                            </div>
                        </div>
                        
                        <div class="items-table">
                            <table class="table table-bordered mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية</th>
                                        <th>الوحدة</th>
                                        <th>سعر الوحدة</th>
                                        <th>الإجمالي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($items_result->num_rows > 0): ?>
                                        <?php $total_items = 0; ?>
                                        <?php while ($item = $items_result->fetch_assoc()): ?>
                                            <?php $total_items += $item['total_price']; ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                                <td><?php echo number_format($item['quantity'], 3); ?></td>
                                                <td><?php echo htmlspecialchars($item['unit'] ?? 'قطعة'); ?></td>
                                                <td><?php echo formatMoney($item['unit_price']); ?></td>
                                                <td class="fw-bold"><?php echo formatMoney($item['total_price']); ?></td>
                                            </tr>
                                        <?php endwhile; ?>
                                        <tr class="table-info">
                                            <td colspan="4" class="text-end fw-bold">إجمالي الأصناف:</td>
                                            <td class="fw-bold"><?php echo formatMoney($total_items); ?></td>
                                        </tr>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <i class="fas fa-box-open fa-2x text-muted mb-2"></i>
                                                <p class="text-muted">لا توجد أصناف في هذه المبيعة</p>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- ملخص المبيعة -->
                <div class="col-lg-4">
                    <div class="summary-card">
                        <h4 class="mb-4">
                            <i class="fas fa-calculator me-2"></i>
                            ملخص المبيعة
                        </h4>
                        
                        <div class="mb-3">
                            <label class="form-label">المبلغ الإجمالي</label>
                            <input type="number" class="form-control" id="total_amount" 
                                   value="<?php echo $sale['total_amount']; ?>" readonly 
                                   style="background-color: rgba(255,255,255,0.1); color: white;">
                        </div>
                        
                        <div class="mb-3">
                            <label for="discount_amount" class="form-label">مبلغ الخصم</label>
                            <input type="number" class="form-control" id="discount_amount" name="discount_amount" 
                                   step="0.01" value="<?php echo $sale['discount_amount']; ?>" onchange="calculateTotal()" 
                                   style="background-color: rgba(255,255,255,0.1); color: white;">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">المبلغ النهائي</label>
                            <input type="number" class="form-control" id="final_amount" 
                                   value="<?php echo $sale['final_amount']; ?>" readonly 
                                   style="background-color: rgba(255,255,255,0.1); color: white;">
                        </div>
                        
                        <div class="mb-4">
                            <label for="paid_amount" class="form-label">المبلغ المدفوع</label>
                            <input type="number" class="form-control" id="paid_amount" name="paid_amount" 
                                   step="0.01" value="<?php echo $sale['paid_amount']; ?>" onchange="calculateRemaining()" 
                                   style="background-color: rgba(255,255,255,0.1); color: white;">
                        </div>
                        
                        <div class="mb-4">
                            <label class="form-label">المبلغ المتبقي</label>
                            <input type="number" class="form-control" id="remaining_amount" 
                                   value="<?php echo $sale['final_amount'] - $sale['paid_amount']; ?>" readonly 
                                   style="background-color: rgba(255,255,255,0.1); color: white;">
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" name="update_sale" class="btn btn-light btn-lg">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                            <a href="view.php?id=<?php echo $sale['id']; ?>" class="btn btn-outline-light">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </div>
                
            </div>
        </form>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // تفعيل Select2
            $('#customer_id').select2({
                placeholder: 'اختر العميل',
                allowClear: true
            });
            
            // حساب المبلغ النهائي عند التحميل
            calculateTotal();
        });
        
        function calculateTotal() {
            const total = parseFloat(document.getElementById('total_amount').value) || 0;
            const discount = parseFloat(document.getElementById('discount_amount').value) || 0;
            const finalAmount = total - discount;
            
            document.getElementById('final_amount').value = finalAmount.toFixed(2);
            calculateRemaining();
        }
        
        function calculateRemaining() {
            const finalAmount = parseFloat(document.getElementById('final_amount').value) || 0;
            const paidAmount = parseFloat(document.getElementById('paid_amount').value) || 0;
            const remaining = finalAmount - paidAmount;
            
            document.getElementById('remaining_amount').value = remaining.toFixed(2);
        }
    </script>

</body>
</html>
