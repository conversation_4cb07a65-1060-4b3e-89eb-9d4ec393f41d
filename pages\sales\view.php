<?php
/**
 * صفحة عرض تفاصيل المبيعة
 * View Sale Details Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

// التحقق من وجود معرف المبيعة
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error_message'] = "معرف المبيعة غير صحيح";
    header("Location: index.php");
    exit();
}

$sale_id = intval($_GET['id']);

// الحصول على بيانات المبيعة
$sale_query = "SELECT s.*, c.name as customer_name, c.phone as customer_phone, c.address as customer_address, 
               u.name as user_name 
               FROM sales s 
               LEFT JOIN customers c ON s.customer_id = c.id 
               LEFT JOIN users u ON s.user_id = u.id 
               WHERE s.id = ?";

$stmt = $conn->prepare($sale_query);
$stmt->bind_param("i", $sale_id);
$stmt->execute();
$sale_result = $stmt->get_result();

if ($sale_result->num_rows === 0) {
    $_SESSION['error_message'] = "المبيعة غير موجودة";
    header("Location: index.php");
    exit();
}

$sale = $sale_result->fetch_assoc();

// الحصول على أصناف المبيعة - دعم الجدولين
$items_query = "";
$table_to_use = "";

// التحقق من وجود جدول sale_details أولاً
$check_sale_details = $conn->query("SHOW TABLES LIKE 'sale_details'");
if ($check_sale_details->num_rows > 0) {
    // فحص وجود بيانات في sale_details
    $check_data = $conn->prepare("SELECT COUNT(*) as count FROM sale_details WHERE sale_id = ?");
    $check_data->bind_param("i", $sale_id);
    $check_data->execute();
    $data_count = $check_data->get_result()->fetch_assoc()['count'];

    if ($data_count > 0) {
        $table_to_use = "sale_details";
        $items_query = "SELECT sd.*, p.name as product_name, p.unit
                        FROM sale_details sd
                        LEFT JOIN products p ON sd.product_id = p.id
                        WHERE sd.sale_id = ?
                        ORDER BY sd.id";
    }
}

// إذا لم توجد بيانات في sale_details، استخدم sale_items
if (empty($table_to_use)) {
    $check_sale_items = $conn->query("SHOW TABLES LIKE 'sale_items'");
    if ($check_sale_items->num_rows > 0) {
        $table_to_use = "sale_items";
        $items_query = "SELECT si.*, p.name as product_name, p.unit
                        FROM sale_items si
                        LEFT JOIN products p ON si.product_id = p.id
                        WHERE si.sale_id = ?
                        ORDER BY si.id";
    }
}

// تنفيذ الاستعلام إذا كان متاحاً
$items_result = null;
if (!empty($items_query)) {
    $stmt = $conn->prepare($items_query);
    $stmt->bind_param("i", $sale_id);
    $stmt->execute();
    $items_result = $stmt->get_result();
}

$page_title = "تفاصيل المبيعة رقم: " . $sale['sale_number'];
$page_icon = "fas fa-eye";

// حساب المبلغ المتبقي
$remaining_amount = $sale['final_amount'] - $sale['paid_amount'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .info-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .summary-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
        }
        .items-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
        }
        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }
        .status-partial {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-unpaid {
            background-color: #f8d7da;
            color: #721c24;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .main-header {
                background: #333 !important;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header no-print">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        تفاصيل المبيعة
                    </h1>
                    <p class="mb-0 mt-2">رقم المبيعة: <?php echo htmlspecialchars($sale['sale_number']); ?></p>
                </div>
                <div class="col-md-6 text-end">
                    <button onclick="window.print()" class="btn btn-light btn-lg me-2">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    <a href="edit.php?id=<?php echo $sale['id']; ?>" class="btn btn-warning btn-lg me-2">
                        <i class="fas fa-edit me-2"></i>
                        تعديل
                    </a>
                    <a href="index.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-list me-2"></i>
                        قائمة المبيعات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show no-print" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            
            <!-- معلومات المبيعة -->
            <div class="col-lg-8">
                
                <!-- معلومات أساسية -->
                <div class="info-card">
                    <h4 class="mb-4">
                        <i class="fas fa-info-circle me-2 text-primary"></i>
                        معلومات المبيعة
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>رقم المبيعة:</strong></td>
                                    <td><?php echo htmlspecialchars($sale['sale_number']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ المبيعة:</strong></td>
                                    <td><?php echo date('Y-m-d', strtotime($sale['sale_date'])); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>العميل:</strong></td>
                                    <td><?php echo htmlspecialchars($sale['customer_name'] ?? 'عميل نقدي'); ?></td>
                                </tr>
                                <?php if (!empty($sale['customer_phone'])): ?>
                                <tr>
                                    <td><strong>هاتف العميل:</strong></td>
                                    <td><?php echo htmlspecialchars($sale['customer_phone']); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>طريقة الدفع:</strong></td>
                                    <td>
                                        <?php
                                        $payment_methods = [
                                            'cash' => 'نقدي',
                                            'card' => 'بطاقة',
                                            'bank_transfer' => 'تحويل بنكي'
                                        ];
                                        echo $payment_methods[$sale['payment_method']] ?? $sale['payment_method'];
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>المستخدم:</strong></td>
                                    <td><?php echo htmlspecialchars($sale['user_name']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($sale['created_at'])); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>حالة الدفع:</strong></td>
                                    <td>
                                        <?php if ($remaining_amount <= 0): ?>
                                            <span class="status-badge status-paid">مدفوع بالكامل</span>
                                        <?php elseif ($sale['paid_amount'] > 0): ?>
                                            <span class="status-badge status-partial">مدفوع جزئياً</span>
                                        <?php else: ?>
                                            <span class="status-badge status-unpaid">غير مدفوع</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php if (!empty($sale['notes'])): ?>
                        <div class="mt-3">
                            <strong>ملاحظات:</strong>
                            <p class="mt-2 p-3 bg-light rounded"><?php echo nl2br(htmlspecialchars($sale['notes'])); ?></p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- أصناف المبيعة -->
                <div class="items-table">
                    <div class="p-3 bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-box me-2"></i>
                            أصناف المبيعة
                        </h4>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>الوحدة</th>
                                    <th>سعر الوحدة</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($items_result && $items_result->num_rows > 0): ?>
                                    <?php while ($item = $items_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                            <td><?php echo number_format($item['quantity'], 3); ?></td>
                                            <td><?php echo htmlspecialchars($item['unit'] ?? 'قطعة'); ?></td>
                                            <td><?php echo formatMoney($item['unit_price']); ?></td>
                                            <td class="fw-bold"><?php echo formatMoney($item['total_price']); ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <i class="fas fa-box-open fa-2x text-muted mb-2"></i>
                                            <p class="text-muted">لا توجد أصناف في هذه المبيعة</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
            </div>

            <!-- ملخص المبيعة -->
            <div class="col-lg-4">
                <div class="summary-card">
                    <h4 class="mb-4">
                        <i class="fas fa-calculator me-2"></i>
                        ملخص المبيعة
                    </h4>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>المبلغ الإجمالي:</span>
                        <strong><?php echo formatMoney($sale['total_amount']); ?></strong>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>مبلغ الخصم:</span>
                        <strong class="text-warning">- <?php echo formatMoney($sale['discount_amount']); ?></strong>
                    </div>
                    
                    <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>المبلغ النهائي:</span>
                        <strong class="fs-5"><?php echo formatMoney($sale['final_amount']); ?></strong>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>المبلغ المدفوع:</span>
                        <strong class="text-success"><?php echo formatMoney($sale['paid_amount']); ?></strong>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-4">
                        <span>المبلغ المتبقي:</span>
                        <strong class="<?php echo ($remaining_amount > 0) ? 'text-warning' : 'text-success'; ?>">
                            <?php echo formatMoney($remaining_amount); ?>
                        </strong>
                    </div>
                    
                    <?php if ($remaining_amount > 0): ?>
                        <div class="alert alert-warning" style="background-color: rgba(255,255,255,0.1); border-color: rgba(255,255,255,0.3); color: white;">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> يوجد مبلغ متبقي على العميل
                        </div>
                    <?php endif; ?>
                    
                    <div class="d-grid gap-2 no-print">
                        <a href="print.php?id=<?php echo $sale['id']; ?>" class="btn btn-light" target="_blank">
                            <i class="fas fa-print me-2"></i>
                            طباعة الفاتورة
                        </a>
                        <a href="edit.php?id=<?php echo $sale['id']; ?>" class="btn btn-outline-light">
                            <i class="fas fa-edit me-2"></i>
                            تعديل المبيعة
                        </a>
                        <a href="index.php" class="btn btn-outline-light">
                            <i class="fas fa-list me-2"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
            
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
