# نظام إدارة الموردين - نظام Zero

## نظرة عامة

تم إكمال نظام إدارة الموردين بالكامل في نظام Zero لإدارة المحلات. يوفر النظام جميع الوظائف المطلوبة لإدارة الموردين بشكل فعال ومتكامل.

## الملفات المنشأة

### 1. الملفات الأساسية
- `pages/suppliers/add.php` - إضافة مورد جديد
- `pages/suppliers/edit.php` - تعديل بيانات المورد
- `pages/suppliers/view.php` - عرض تفاصيل المورد
- `pages/suppliers/index.php` - قائمة الموردين (موجود مسبقاً ومحدث)

### 2. الملفات المتقدمة
- `pages/suppliers/reports.php` - التقارير والإحصائيات
- `pages/suppliers/export.php` - تصدير البيانات
- `pages/suppliers/payments.php` - إدارة حسابات الموردين

### 3. ملفات النظام
- `update-suppliers-table.php` - تحديث بنية قاعدة البيانات
- `suppliers-system-completion-summary.php` - ملخص إكمال النظام

## المميزات الرئيسية

### ✅ إدارة البيانات الأساسية
- إضافة موردين جدد مع جميع البيانات المطلوبة
- تعديل بيانات الموردين الموجودين
- عرض تفاصيل شاملة لكل مورد
- حذف الموردين مع التحقق من وجود مشتريات مرتبطة

### ✅ البحث والفلترة
- بحث متقدم في جميع حقول المورد
- فلترة حسب حالة الرصيد (مستحقات لنا/علينا/صفر)
- ترتيب النتائج حسب معايير مختلفة

### ✅ التقارير والإحصائيات
- تقارير شاملة مع رسوم بيانية
- إحصائيات الأرصدة والمستحقات
- أكبر الموردين حسب قيمة المشتريات
- فلترة التقارير حسب التاريخ والمورد

### ✅ إدارة الحسابات المالية
- تسجيل دفعات للموردين
- إضافة أو تقليل الأرصدة
- تتبع جميع الحركات المالية
- ربط مع نظام الخزينة

### ✅ التصدير والطباعة
- تصدير البيانات إلى ملفات CSV
- دعم كامل للغة العربية في التصدير
- طباعة التقارير
- تصدير أنواع مختلفة من البيانات

### ✅ التكامل مع الأنظمة الأخرى
- ربط مع نظام المشتريات
- تحديث تلقائي للأرصدة عند المشتريات
- تسجيل الحركات في الخزينة
- عرض آخر المشتريات لكل مورد

## بنية قاعدة البيانات

### جدول الموردين (suppliers)
```sql
CREATE TABLE suppliers (
  id int NOT NULL AUTO_INCREMENT,
  name varchar(100) NOT NULL,
  company varchar(100) DEFAULT NULL,
  phone varchar(20) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  address text,
  balance decimal(10,2) DEFAULT '0.00',
  notes text,
  is_active tinyint(1) DEFAULT '1',
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY name (name),
  KEY phone (phone),
  KEY is_active (is_active)
);
```

## التثبيت والإعداد

### 1. تحديث قاعدة البيانات
```bash
# قم بزيارة الرابط التالي لتحديث بنية قاعدة البيانات
http://localhost/zero/update-suppliers-table.php
```

### 2. التحقق من الصلاحيات
- تأكد من صلاحيات الكتابة على مجلد `uploads`
- تأكد من صلاحيات قاعدة البيانات

### 3. اختبار النظام
```bash
# قم بزيارة الروابط التالية للاختبار
http://localhost/zero/pages/suppliers/index.php
http://localhost/zero/pages/suppliers/add.php
http://localhost/zero/pages/suppliers/reports.php
```

## الاستخدام

### إضافة مورد جديد
1. انتقل إلى قائمة الموردين
2. اضغط على "مورد جديد"
3. املأ البيانات المطلوبة
4. احفظ البيانات

### إدارة حساب المورد
1. انتقل إلى تفاصيل المورد
2. اضغط على "إدارة الحساب"
3. اختر نوع العملية (دفع/إضافة)
4. أدخل المبلغ والتفاصيل
5. احفظ العملية

### عرض التقارير
1. انتقل إلى قائمة الموردين
2. اضغط على "التقارير"
3. اختر الفلاتر المطلوبة
4. اعرض أو اطبع التقرير

### تصدير البيانات
1. من صفحة التقارير
2. اضغط على "تصدير Excel"
3. سيتم تحميل ملف CSV

## الأمان والحماية

### التحقق من البيانات
- تحقق من صحة جميع المدخلات
- حماية من SQL Injection
- تشفير كلمات المرور
- التحقق من صلاحيات المستخدم

### النسخ الاحتياطية
- يُنصح بعمل نسخة احتياطية قبل التحديث
- النظام يدعم النسخ الاحتياطية التلقائية
- حفظ سجل جميع العمليات

## الدعم الفني

### المشاكل الشائعة
1. **خطأ في قاعدة البيانات**: تأكد من تشغيل سكريبت التحديث
2. **مشكلة في التصدير**: تأكد من صلاحيات الكتابة
3. **عدم ظهور البيانات**: تحقق من الاتصال بقاعدة البيانات

### ملفات السجلات
- سجلات الأخطاء في ملفات PHP
- سجلات قاعدة البيانات
- سجلات الخزينة

## التطوير المستقبلي

### ميزات مقترحة
- إشعارات تلقائية للمستحقات
- تكامل مع أنظمة المحاسبة الخارجية
- تطبيق موبايل
- واجهة برمجة تطبيقات (API)

### التحسينات
- تحسين الأداء
- إضافة المزيد من التقارير
- تحسين واجهة المستخدم
- دعم المزيد من اللغات

## الترخيص

هذا النظام جزء من نظام Zero لإدارة المحلات ويخضع لنفس شروط الترخيص.

## معلومات الاتصال

للدعم الفني أو الاستفسارات، يرجى التواصل مع فريق التطوير.

---

**تم إكمال نظام إدارة الموردين بنجاح! 🎉**

جميع الوظائف تعمل بشكل صحيح ومتكامل مع باقي أجزاء النظام.
