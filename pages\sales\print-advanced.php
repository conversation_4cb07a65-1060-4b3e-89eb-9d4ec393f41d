<?php
/**
 * طباعة فاتورة المبيعة - النظام المتقدم
 * Print Sale Invoice - Advanced System
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";
require_once "../../includes/advanced-balance-functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

// التحقق من وجود معرف المبيعة
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error_message'] = "معرف المبيعة غير صحيح";
    header("Location: index.php");
    exit();
}

$sale_id = intval($_GET['id']);

// الحصول على بيانات المبيعة
$sale_query = "SELECT s.*, c.name as customer_name, c.phone as customer_phone, 
                      u.name as user_name
               FROM sales s 
               LEFT JOIN customers c ON s.customer_id = c.id 
               LEFT JOIN users u ON s.user_id = u.id 
               WHERE s.id = ?";

$stmt = $conn->prepare($sale_query);
$stmt->bind_param("i", $sale_id);
$stmt->execute();
$sale_result = $stmt->get_result();

if ($sale_result->num_rows === 0) {
    $_SESSION['error_message'] = "المبيعة غير موجودة";
    header("Location: index.php");
    exit();
}

$sale = $sale_result->fetch_assoc();

// الحصول على أصناف المبيعة - دعم الجدولين
$items_query = "";
$table_to_use = "";

// التحقق من وجود جدول sale_details أولاً
$check_sale_details = $conn->query("SHOW TABLES LIKE 'sale_details'");
if ($check_sale_details->num_rows > 0) {
    // فحص وجود بيانات في sale_details
    $check_data = $conn->prepare("SELECT COUNT(*) as count FROM sale_details WHERE sale_id = ?");
    $check_data->bind_param("i", $sale_id);
    $check_data->execute();
    $data_count = $check_data->get_result()->fetch_assoc()['count'];
    
    if ($data_count > 0) {
        $table_to_use = "sale_details";
        $items_query = "SELECT sd.*, p.name as product_name, p.unit 
                        FROM sale_details sd 
                        LEFT JOIN products p ON sd.product_id = p.id 
                        WHERE sd.sale_id = ? 
                        ORDER BY sd.id";
    }
}

// إذا لم توجد بيانات في sale_details، استخدم sale_items
if (empty($table_to_use)) {
    $check_sale_items = $conn->query("SHOW TABLES LIKE 'sale_items'");
    if ($check_sale_items->num_rows > 0) {
        $table_to_use = "sale_items";
        $items_query = "SELECT si.*, p.name as product_name, p.unit 
                        FROM sale_items si 
                        LEFT JOIN products p ON si.product_id = p.id 
                        WHERE si.sale_id = ? 
                        ORDER BY si.id";
    }
}

// تنفيذ الاستعلام إذا كان متاحاً
$items_result = null;
if (!empty($items_query)) {
    $stmt = $conn->prepare($items_query);
    $stmt->bind_param("i", $sale_id);
    $stmt->execute();
    $items_result = $stmt->get_result();
}

// الحصول على إعدادات المتجر
$store_name = getSetting('store_name', 'متجر Zero');
$store_address = getSetting('store_address', '');
$store_phone = getSetting('store_phone', '');
$store_currency = getSetting('store_currency', 'ريال');
$receipt_footer = getSetting('receipt_footer', 'شكراً لزيارتكم');
$store_logo = getSetting('store_logo', '');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة فاتورة رقم <?php echo $sale['sale_number'] ?? $sale['id']; ?> - <?php echo $store_name; ?></title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            padding: 20px;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .store-info {
            flex: 1;
            text-align: right;
        }
        
        .store-name {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .store-details {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .logo-section {
            flex: 0 0 auto;
        }
        
        .store-logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid white;
            object-fit: cover;
        }
        
        .logo-placeholder {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid white;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255,255,255,0.2);
            font-size: 12px;
            text-align: center;
        }
        
        .invoice-info {
            flex: 1;
            text-align: left;
        }
        
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .invoice-meta {
            display: flex;
            flex-direction: column;
            gap: 8px;
            font-size: 14px;
        }
        
        .invoice-details {
            padding: 30px;
        }
        
        .customer-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-right: 4px solid #667eea;
        }
        
        .customer-info h4 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .items-table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .items-table th,
        .items-table td {
            padding: 15px 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .items-table th {
            font-weight: 600;
            font-size: 14px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }
        
        .items-table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .items-table tbody tr:last-child td {
            border-bottom: none;
        }
        
        .summary-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-top: 25px;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
            font-size: 16px;
        }
        
        .summary-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            color: #667eea;
            background: white;
            padding: 15px;
            border-radius: 6px;
            margin-top: 10px;
        }
        
        .payment-info {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border-right: 4px solid #28a745;
        }
        
        .payment-info h4 {
            color: #28a745;
            margin-bottom: 10px;
        }
        
        .footer-section {
            background: #667eea;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 14px;
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        
        .print-button:hover {
            background: #218838;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .invoice-container {
                box-shadow: none;
                border-radius: 0;
            }
            
            .print-button {
                display: none;
            }
        }
        
        @media (max-width: 768px) {
            .header-row {
                flex-direction: column;
                text-align: center;
            }
            
            .invoice-meta {
                align-items: center;
            }
            
            .items-table {
                font-size: 12px;
            }
            
            .items-table th,
            .items-table td {
                padding: 8px 5px;
            }
        }
    </style>
</head>
<body>
    
    <button class="print-button no-print" onclick="window.print()">
        <i class="fas fa-print"></i> طباعة
    </button>
    
    <div class="invoice-container">
        
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="header-row">
                <!-- بيانات المحل -->
                <div class="store-info">
                    <div class="store-name"><?php echo htmlspecialchars($store_name); ?></div>
                    <div class="store-details">
                        <?php if (!empty($store_address)): ?>
                            <div><?php echo htmlspecialchars($store_address); ?></div>
                        <?php endif; ?>
                        <?php if (!empty($store_phone)): ?>
                            <div>هاتف: <?php echo htmlspecialchars($store_phone); ?></div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- الشعار -->
                <div class="logo-section">
                    <?php if (!empty($store_logo) && file_exists("../../" . $store_logo)): ?>
                        <img src="../../<?php echo $store_logo; ?>" alt="شعار المحل" class="store-logo">
                    <?php else: ?>
                        <div class="logo-placeholder">
                            شعار المحل
                        </div>
                    <?php endif; ?>
                </div>

                <!-- بيانات الفاتورة -->
                <div class="invoice-info">
                    <div class="invoice-title">فاتورة مبيعة متقدمة</div>
                    <div class="invoice-meta">
                        <div><strong>رقم الفاتورة:</strong> <?php echo $sale['sale_number'] ?? $sale['id']; ?></div>
                        <div><strong>التاريخ:</strong> <?php echo date('Y-m-d', strtotime($sale['sale_date'])); ?></div>
                        <div><strong>الوقت:</strong> <?php echo date('H:i', strtotime($sale['created_at'])); ?></div>
                        <div><strong>الكاشير:</strong> <?php echo htmlspecialchars($sale['user_name']); ?></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- تفاصيل الفاتورة -->
        <div class="invoice-details">
            
            <!-- معلومات العميل -->
            <?php if (!empty($sale['customer_name']) && $sale['customer_name'] != 'عميل نقدي'): ?>
                <div class="customer-info">
                    <h4>بيانات العميل</h4>
                    <div><strong>الاسم:</strong> <?php echo htmlspecialchars($sale['customer_name']); ?></div>
                    <?php if (!empty($sale['customer_phone'])): ?>
                        <div><strong>الهاتف:</strong> <?php echo htmlspecialchars($sale['customer_phone']); ?></div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <!-- جدول الأصناف -->
            <table class="items-table">
                <thead>
                    <tr>
                        <th>م</th>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>الوحدة</th>
                        <th>سعر الوحدة</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($items_result && $items_result->num_rows > 0): ?>
                        <?php $counter = 1; ?>
                        <?php while ($item = $items_result->fetch_assoc()): ?>
                            <tr>
                                <td><?php echo $counter++; ?></td>
                                <td style="text-align: right;"><?php echo htmlspecialchars($item['product_name']); ?></td>
                                <td><?php echo number_format($item['quantity'], 3); ?></td>
                                <td><?php echo htmlspecialchars($item['unit'] ?? 'قطعة'); ?></td>
                                <td><?php echo number_format($item['unit_price'], 2); ?> <?php echo $store_currency; ?></td>
                                <td><?php echo number_format($item['total_price'], 2); ?> <?php echo $store_currency; ?></td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" style="text-align: center; color: #666;">
                                لا توجد أصناف
                                <?php if (!empty($table_to_use)): ?>
                                    <br><small style="color: #999;">البحث في: <?php echo $table_to_use; ?></small>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            
            <!-- ملخص المبالغ -->
            <div class="summary-section">
                <div class="summary-row">
                    <span>المبلغ الإجمالي:</span>
                    <span><?php echo number_format($sale['total_amount'], 2); ?> <?php echo $store_currency; ?></span>
                </div>
                
                <?php if ($sale['discount_amount'] > 0): ?>
                    <div class="summary-row">
                        <span>الخصم:</span>
                        <span><?php echo number_format($sale['discount_amount'], 2); ?> <?php echo $store_currency; ?></span>
                    </div>
                <?php endif; ?>
                
                <div class="summary-row">
                    <span>المبلغ النهائي:</span>
                    <span><?php echo number_format($sale['final_amount'], 2); ?> <?php echo $store_currency; ?></span>
                </div>
            </div>
            
            <!-- معلومات الدفع -->
            <div class="payment-info">
                <h4>تفاصيل الدفع</h4>
                <div class="summary-row">
                    <span>المبلغ المدفوع:</span>
                    <span><?php echo number_format($sale['paid_amount'], 2); ?> <?php echo $store_currency; ?></span>
                </div>
                
                <?php 
                $remaining = $sale['final_amount'] - $sale['paid_amount'];
                if ($remaining > 0): 
                ?>
                    <div class="summary-row">
                        <span>المبلغ المتبقي:</span>
                        <span style="color: #dc3545;"><?php echo number_format($remaining, 2); ?> <?php echo $store_currency; ?></span>
                    </div>
                <?php endif; ?>
                
                <div class="summary-row">
                    <span>طريقة الدفع:</span>
                    <span><?php
                        $payment_methods = [
                            'cash' => 'نقدي',
                            'card' => 'بطاقة',
                            'bank_transfer' => 'تحويل بنكي',
                            'wallet' => 'من الرصيد',
                            'mixed' => 'مختلط'
                        ];
                        echo $payment_methods[$sale['payment_method']] ?? $sale['payment_method'];
                    ?></span>
                </div>
            </div>
            
            <?php if (!empty($sale['notes'])): ?>
                <div class="customer-info">
                    <h4>ملاحظات</h4>
                    <p><?php echo nl2br(htmlspecialchars($sale['notes'])); ?></p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- تذييل الفاتورة -->
        <div class="footer-section">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم الطباعة بتاريخ: <?php echo date('Y-m-d H:i'); ?></p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند التحميل (اختياري)
        // window.onload = function() { window.print(); }
        
        // إضافة اختصار لوحة المفاتيح للطباعة
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>

</body>
</html>
