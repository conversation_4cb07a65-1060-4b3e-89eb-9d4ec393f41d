<?php
/**
 * سكريبت تحديث جدول الموردين
 * Update Suppliers Table Script
 */

// استدعاء ملف الإعدادات
require_once "config/db_config.php";

// بدء الجلسة
session_start();

// التحقق من صلاحيات المدير
if (!isset($_SESSION["user_id"]) || $_SESSION["user_role"] !== 'admin') {
    die("غير مسموح لك بالوصول إلى هذه الصفحة");
}

$success_messages = [];
$error_messages = [];

try {
    // بدء المعاملة
    $conn->begin_transaction();
    
    // دالة للتحقق من وجود عمود
    function columnExists($conn, $table, $column) {
        $query = "SHOW COLUMNS FROM `$table` LIKE '$column'";
        $result = $conn->query($query);
        return $result && $result->num_rows > 0;
    }
    
    // دالة لإضافة عمود بأمان
    function addColumnSafely($conn, $table, $column, $definition, &$messages) {
        if (!columnExists($conn, $table, $column)) {
            $query = "ALTER TABLE `$table` ADD COLUMN `$column` $definition";
            if ($conn->query($query)) {
                $messages[] = "تم إضافة العمود '$column' إلى جدول '$table'";
                return true;
            } else {
                throw new Exception("فشل في إضافة العمود '$column': " . $conn->error);
            }
        } else {
            $messages[] = "العمود '$column' موجود بالفعل في جدول '$table'";
            return false;
        }
    }
    
    $success_messages[] = "=== بدء تحديث جدول الموردين ===";
    
    // إضافة الأعمدة المفقودة
    addColumnSafely($conn, 'suppliers', 'company', "VARCHAR(100) NULL COMMENT 'اسم الشركة'", $success_messages);
    addColumnSafely($conn, 'suppliers', 'is_active', "TINYINT(1) DEFAULT 1 COMMENT 'حالة المورد'", $success_messages);
    addColumnSafely($conn, 'suppliers', 'updated_at', "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث'", $success_messages);
    
    // إضافة فهارس للأداء
    $indexes = [
        "ALTER TABLE `suppliers` ADD INDEX `idx_name` (`name`)",
        "ALTER TABLE `suppliers` ADD INDEX `idx_phone` (`phone`)",
        "ALTER TABLE `suppliers` ADD INDEX `idx_is_active` (`is_active`)"
    ];
    
    foreach ($indexes as $index_query) {
        try {
            $conn->query($index_query);
            $success_messages[] = "تم إضافة فهرس جديد";
        } catch (Exception $e) {
            // تجاهل الأخطاء إذا كان الفهرس موجود بالفعل
            if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                $error_messages[] = "تحذير: " . $e->getMessage();
            }
        }
    }
    
    // تحديث البيانات الموجودة
    $update_query = "UPDATE suppliers SET updated_at = created_at WHERE updated_at IS NULL";
    if ($conn->query($update_query)) {
        $affected_rows = $conn->affected_rows;
        $success_messages[] = "تم تحديث $affected_rows سجل موجود";
    }
    
    // تأكيد المعاملة
    $conn->commit();
    $success_messages[] = "=== اكتمل تحديث جدول الموردين بنجاح! ===";
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    $conn->rollback();
    $error_messages[] = "خطأ في التحديث: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث جدول الموردين | نظام Zero</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- نمط الخط العربي -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .container {
            max-width: 800px;
        }
        
        .update-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .card-body {
            padding: 2rem;
        }
        
        .message-item {
            padding: 0.75rem 1rem;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .message-success {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .message-error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .btn {
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="update-card">
            <div class="card-header">
                <h2 class="mb-0">
                    <i class="fas fa-database me-2"></i>
                    تحديث جدول الموردين
                </h2>
                <p class="mb-0 mt-2">تحديث بنية قاعدة البيانات لجدول الموردين</p>
            </div>
            
            <div class="card-body">
                <?php if (!empty($success_messages)): ?>
                    <h5 class="text-success mb-3">
                        <i class="fas fa-check-circle me-2"></i>
                        رسائل النجاح:
                    </h5>
                    <?php foreach ($success_messages as $message): ?>
                        <div class="message-item message-success">
                            <i class="fas fa-check me-2"></i>
                            <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <?php if (!empty($error_messages)): ?>
                    <h5 class="text-danger mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        رسائل الخطأ:
                    </h5>
                    <?php foreach ($error_messages as $message): ?>
                        <div class="message-item message-error">
                            <i class="fas fa-times me-2"></i>
                            <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <div class="text-center mt-4">
                    <a href="pages/suppliers/index.php" class="btn btn-success me-2">
                        <i class="fas fa-truck me-2"></i>
                        إدارة الموردين
                    </a>
                    <a href="pages/suppliers/add.php" class="btn btn-primary me-2">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مورد جديد
                    </a>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-home me-2"></i>
                        الصفحة الرئيسية
                    </a>
                </div>
                
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات التحديث:
                    </h6>
                    <ul class="text-muted small mb-0">
                        <li>تم إضافة عمود "company" لاسم الشركة</li>
                        <li>تم إضافة عمود "is_active" لحالة المورد</li>
                        <li>تم إضافة عمود "updated_at" لتاريخ آخر تحديث</li>
                        <li>تم إضافة فهارس للأداء</li>
                        <li>تم تحديث البيانات الموجودة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
