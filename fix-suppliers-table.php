<?php
/**
 * إصلاح جدول الموردين وإضافة الأعمدة المفقودة
 * Fix Suppliers Table and Add Missing Columns
 */

// استدعاء ملفات الإعدادات
require_once "config/db_config.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "إصلاح جدول الموردين";
$updates = [];
$errors = [];

// معالجة التحديث
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_table'])) {
    try {
        $conn->begin_transaction();
        
        // التحقق من وجود الأعمدة وإضافة المفقود منها
        $columns_to_add = [
            'notes' => "ALTER TABLE suppliers ADD COLUMN notes TEXT NULL COMMENT 'ملاحظات' AFTER balance",
            'company' => "ALTER TABLE suppliers ADD COLUMN company VARCHAR(255) NULL COMMENT 'اسم الشركة' AFTER name",
            'wallet_balance' => "ALTER TABLE suppliers ADD COLUMN wallet_balance DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ' AFTER balance",
            'debt_balance' => "ALTER TABLE suppliers ADD COLUMN debt_balance DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون' AFTER wallet_balance",
            'credit_limit' => "ALTER TABLE suppliers ADD COLUMN credit_limit DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'حد الائتمان' AFTER debt_balance",
            'last_transaction_date' => "ALTER TABLE suppliers ADD COLUMN last_transaction_date TIMESTAMP NULL COMMENT 'تاريخ آخر معاملة' AFTER credit_limit"
        ];
        
        foreach ($columns_to_add as $column => $sql) {
            $check_query = "SHOW COLUMNS FROM suppliers LIKE '$column'";
            $result = $conn->query($check_query);
            
            if ($result->num_rows == 0) {
                $conn->query($sql);
                $updates[] = "تم إضافة العمود: $column";
            } else {
                $updates[] = "العمود $column موجود بالفعل";
            }
        }
        
        // إضافة مؤشرات للأداء
        $indexes = [
            'idx_suppliers_name' => "CREATE INDEX idx_suppliers_name ON suppliers(name)",
            'idx_suppliers_phone' => "CREATE INDEX idx_suppliers_phone ON suppliers(phone)",
            'idx_suppliers_company' => "CREATE INDEX idx_suppliers_company ON suppliers(company)"
        ];
        
        foreach ($indexes as $index_name => $sql) {
            try {
                $conn->query($sql);
                $updates[] = "تم إضافة المؤشر: $index_name";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                    $updates[] = "تحذير في المؤشر $index_name: " . $e->getMessage();
                } else {
                    $updates[] = "المؤشر $index_name موجود بالفعل";
                }
            }
        }
        
        $conn->commit();
        
    } catch (Exception $e) {
        $conn->rollback();
        $errors[] = "خطأ في التحديث: " . $e->getMessage();
    }
}

// فحص حالة الجدول
$table_status = [];

try {
    // فحص وجود الجدول
    $check_table = $conn->query("SHOW TABLES LIKE 'suppliers'");
    $table_status['table_exists'] = $check_table->num_rows > 0;
    
    if ($table_status['table_exists']) {
        // فحص الأعمدة
        $required_columns = ['name', 'phone', 'email', 'address', 'balance', 'notes', 'company', 'wallet_balance', 'debt_balance', 'credit_limit', 'last_transaction_date'];
        
        foreach ($required_columns as $column) {
            $check_query = "SHOW COLUMNS FROM suppliers LIKE '$column'";
            $result = $conn->query($check_query);
            $table_status["has_$column"] = $result->num_rows > 0;
        }
        
        // عدد الموردين
        $count_query = "SELECT COUNT(*) as count FROM suppliers";
        $count_result = $conn->query($count_query);
        $table_status['suppliers_count'] = $count_result->fetch_assoc()['count'];
    }
    
} catch (Exception $e) {
    $errors[] = "خطأ في فحص الجدول: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .status-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        .status-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .status-danger {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-tools me-3"></i>
                إصلاح جدول الموردين
            </h1>
            <p class="lead">إضافة الأعمدة المفقودة وإصلاح مشكلة "notes"</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($updates)): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>تحديثات مطبقة:</h5>
                <ul class="mb-0">
                    <?php foreach ($updates as $update): ?>
                        <li><?php echo htmlspecialchars($update); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle me-2"></i>أخطاء:</h5>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- حالة الجدول -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-database me-2"></i>حالة جدول الموردين</h3>
            </div>
            <div class="card-body">
                
                <?php if ($table_status['table_exists']): ?>
                    <div class="status-item status-success">
                        <h6><i class="fas fa-check me-2"></i>جدول suppliers موجود</h6>
                        <p class="mb-0">يحتوي على <?php echo $table_status['suppliers_count']; ?> مورد</p>
                    </div>
                    
                    <h6 class="mt-3">الأعمدة الأساسية:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <?php foreach (['name', 'phone', 'email', 'address', 'balance'] as $column): ?>
                                <div class="status-item <?php echo $table_status["has_$column"] ? 'status-success' : 'status-danger'; ?>">
                                    <span><?php echo $column; ?></span>
                                    <i class="fas fa-<?php echo $table_status["has_$column"] ? 'check text-success' : 'times text-danger'; ?> float-end"></i>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="col-md-6">
                            <?php foreach (['notes', 'company', 'wallet_balance', 'debt_balance', 'credit_limit', 'last_transaction_date'] as $column): ?>
                                <div class="status-item <?php echo $table_status["has_$column"] ? 'status-success' : 'status-warning'; ?>">
                                    <span><?php echo $column; ?></span>
                                    <i class="fas fa-<?php echo $table_status["has_$column"] ? 'check text-success' : 'times text-warning'; ?> float-end"></i>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                <?php else: ?>
                    <div class="status-item status-danger">
                        <h6><i class="fas fa-times me-2"></i>جدول suppliers غير موجود</h6>
                        <p class="mb-0">يجب إنشاء الجدول أولاً</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- شرح المشكلة -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-question-circle me-2"></i>شرح المشكلة</h3>
            </div>
            <div class="card-body">
                <h5>خطأ "Unknown column 'notes' in 'field list'":</h5>
                <ul>
                    <li><strong>السبب:</strong> الكود يحاول إدراج بيانات في عمود <code>notes</code> غير موجود</li>
                    <li><strong>التأثير:</strong> فشل في إضافة موردين جدد</li>
                    <li><strong>الحل:</strong> إضافة العمود المفقود أو تعديل الكود ليتعامل مع غيابه</li>
                </ul>
                
                <h5 class="mt-3">الأعمدة المطلوبة:</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>أساسية:</h6>
                        <ul>
                            <li><code>name</code> - اسم المورد</li>
                            <li><code>phone</code> - رقم الهاتف</li>
                            <li><code>email</code> - البريد الإلكتروني</li>
                            <li><code>address</code> - العنوان</li>
                            <li><code>balance</code> - الرصيد</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>إضافية:</h6>
                        <ul>
                            <li><code>notes</code> - ملاحظات</li>
                            <li><code>company</code> - اسم الشركة</li>
                            <li><code>wallet_balance</code> - الرصيد المحفوظ</li>
                            <li><code>debt_balance</code> - رصيد الديون</li>
                            <li><code>credit_limit</code> - حد الائتمان</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- إصلاح الجدول -->
        <?php 
        $needs_fix = false;
        if ($table_status['table_exists']) {
            $required_for_fix = ['notes', 'company', 'wallet_balance', 'debt_balance'];
            foreach ($required_for_fix as $col) {
                if (!$table_status["has_$col"]) {
                    $needs_fix = true;
                    break;
                }
            }
        }
        ?>
        
        <?php if ($needs_fix): ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-wrench me-2"></i>إصلاح الجدول</h3>
                </div>
                <div class="card-body">
                    <p>سيتم إضافة الأعمدة المفقودة التالية:</p>
                    <ul>
                        <?php if (!$table_status['has_notes']): ?><li><code>notes</code> - ملاحظات</li><?php endif; ?>
                        <?php if (!$table_status['has_company']): ?><li><code>company</code> - اسم الشركة</li><?php endif; ?>
                        <?php if (!$table_status['has_wallet_balance']): ?><li><code>wallet_balance</code> - الرصيد المحفوظ</li><?php endif; ?>
                        <?php if (!$table_status['has_debt_balance']): ?><li><code>debt_balance</code> - رصيد الديون</li><?php endif; ?>
                        <?php if (!$table_status['has_credit_limit']): ?><li><code>credit_limit</code> - حد الائتمان</li><?php endif; ?>
                        <?php if (!$table_status['has_last_transaction_date']): ?><li><code>last_transaction_date</code> - تاريخ آخر معاملة</li><?php endif; ?>
                    </ul>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من إصلاح الجدول؟')">
                        <button type="submit" name="fix_table" class="btn btn-success btn-lg">
                            <i class="fas fa-tools me-2"></i>
                            إصلاح الجدول الآن
                        </button>
                    </form>
                </div>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-check-circle me-2"></i>الجدول سليم!</h3>
                </div>
                <div class="card-body">
                    <p>جميع الأعمدة المطلوبة موجودة. يمكنك الآن إضافة موردين جدد بدون مشاكل.</p>
                    
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="pages/suppliers/add.php" class="btn btn-primary btn-lg me-md-2">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مورد جديد
                        </a>
                        <a href="pages/suppliers/index.php" class="btn btn-info btn-lg">
                            <i class="fas fa-list me-2"></i>
                            عرض الموردين
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <h2>🔧 إصلاح جدول الموردين! 🔧</h2>
            <p class="lead">حل مشكلة الأعمدة المفقودة</p>
            
            <a href="index.php" class="btn btn-light btn-lg mt-3">
                <i class="fas fa-home me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
