<?php
/**
 * صفحة إدارة أرصدة العملاء المتقدمة
 * Advanced Customer Balance Management Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";
require_once "../../includes/advanced-balance-functions.php";
require_once "../../includes/session-helper.php";

// التحقق من تسجيل الدخول
requireLogin();

$page_title = "إدارة أرصدة العملاء المتقدمة";
$page_icon = "fas fa-balance-scale";

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $customer_id = intval($_POST['customer_id']);
        $action = clean($conn, $_POST['action']);
        $amount = floatval($_POST['amount']);
        $description = clean($conn, $_POST['description']);
        
        if ($customer_id <= 0 || $amount <= 0) {
            throw new Exception("بيانات غير صحيحة");
        }
        
        $success = false;
        $message = "";
        
        switch ($action) {
            case 'add_wallet':
                $success = addWalletBalance('customer', $customer_id, $amount, 'manual', 0, $description, $_SESSION['user_id']);
                $message = "تم إضافة " . formatMoney($amount) . " للرصيد المحفوظ";
                break;
                
            case 'use_wallet':
                $used = useWalletBalance('customer', $customer_id, $amount, 'manual', 0, $description, $_SESSION['user_id']);
                $success = $used > 0;
                $message = "تم استخدام " . formatMoney($used) . " من الرصيد المحفوظ";
                break;
                
            case 'add_debt':
                $success = addDebtBalance('customer', $customer_id, $amount, 'manual', 0, $description, $_SESSION['user_id'], false);
                $message = "تم إضافة دين بمبلغ " . formatMoney($amount);
                break;
                
            case 'pay_debt':
                // دفع دين (تقليل رصيد الديون)
                $balances = getEntityBalances('customer', $customer_id);
                $currentDebt = floatval($balances['debt_balance']);
                $payAmount = min($amount, $currentDebt);
                
                if ($payAmount > 0) {
                    $update_query = "UPDATE customers SET debt_balance = debt_balance - ?, last_transaction_date = NOW() WHERE id = ?";
                    $stmt = $conn->prepare($update_query);
                    $stmt->bind_param("di", $payAmount, $customer_id);
                    $success = $stmt->execute();
                    
                    if ($success) {
                        // تسجيل المعاملة
                        $insert_transaction = "INSERT INTO balance_transactions (
                            entity_type, entity_id, transaction_type, amount,
                            wallet_balance_before, wallet_balance_after,
                            debt_balance_before, debt_balance_after,
                            reference_type, reference_id, description, user_id
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                        
                        $new_debt = $currentDebt - $payAmount;
                        $wallet_balance = floatval($balances['wallet_balance']);
                        
                        $trans_stmt = $conn->prepare($insert_transaction);
                        $entity_type = 'customer';
                        $transaction_type = 'debt_pay';
                        $reference_type = 'manual';
                        $reference_id = null;
                        $trans_stmt->bind_param("sisdddddssi", 
                            $entity_type, $customer_id, $transaction_type, $payAmount,
                            $wallet_balance, $wallet_balance,
                            $currentDebt, $new_debt,
                            $reference_type, $reference_id, $description, $_SESSION['user_id']
                        );
                        $trans_stmt->execute();
                        
                        $message = "تم دفع " . formatMoney($payAmount) . " من الديون";
                    }
                }
                break;
        }
        
        if ($success) {
            $_SESSION['success_message'] = $message;
        } else {
            $_SESSION['error_message'] = "فشل في تنفيذ العملية";
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = "خطأ: " . $e->getMessage();
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// التحقق من وجود النظام المتقدم
$check_advanced = $conn->query("SHOW COLUMNS FROM customers LIKE 'debt_balance'");
$has_advanced_system = $check_advanced->num_rows > 0;

// الحصول على العملاء مع أرصدتهم
if ($has_advanced_system) {
    $customers_query = "SELECT c.*,
                               COALESCE(c.wallet_balance, 0) as wallet_balance,
                               COALESCE(c.debt_balance, 0) as debt_balance,
                               (COALESCE(c.wallet_balance, 0) - COALESCE(c.debt_balance, 0)) as net_balance,
                               CASE
                                   WHEN (COALESCE(c.wallet_balance, 0) - COALESCE(c.debt_balance, 0)) > 0 THEN 'دائن'
                                   WHEN (COALESCE(c.wallet_balance, 0) - COALESCE(c.debt_balance, 0)) < 0 THEN 'مدين'
                                   ELSE 'متعادل'
                               END as balance_status,
                               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id) as sales_count,
                               (SELECT COALESCE(SUM(final_amount - paid_amount), 0) FROM sales WHERE customer_id = c.id AND final_amount > paid_amount) as calculated_debt
                        FROM customers c
                        WHERE c.id > 1
                        ORDER BY c.name";
} else {
    $customers_query = "SELECT c.*,
                               0 as wallet_balance,
                               CASE WHEN c.balance < 0 THEN ABS(c.balance) ELSE 0 END as debt_balance,
                               c.balance as net_balance,
                               CASE
                                   WHEN c.balance > 0 THEN 'دائن'
                                   WHEN c.balance < 0 THEN 'مدين'
                                   ELSE 'متعادل'
                               END as balance_status,
                               (SELECT COUNT(*) FROM sales WHERE customer_id = c.id) as sales_count,
                               (SELECT COALESCE(SUM(final_amount - paid_amount), 0) FROM sales WHERE customer_id = c.id AND final_amount > paid_amount) as calculated_debt
                        FROM customers c
                        WHERE c.id > 1
                        ORDER BY c.name";
}

$customers_result = $conn->query($customers_query);

// الحصول على ملخص الأرصدة
try {
    $summary = getCustomersBalanceSummary();
} catch (Exception $e) {
    // في حالة فشل الدالة، استخدم استعلام مباشر
    if ($has_advanced_system) {
        $summary_query = "SELECT
                            COUNT(*) as total_customers,
                            COUNT(CASE WHEN COALESCE(wallet_balance, 0) > 0 THEN 1 END) as customers_with_wallet,
                            COUNT(CASE WHEN COALESCE(debt_balance, 0) > 0 THEN 1 END) as customers_with_debt,
                            COALESCE(SUM(wallet_balance), 0) as total_wallet_balance,
                            COALESCE(SUM(debt_balance), 0) as total_debt_balance,
                            COALESCE(SUM(wallet_balance - debt_balance), 0) as net_balance
                          FROM customers WHERE id > 1";
    } else {
        $summary_query = "SELECT
                            COUNT(*) as total_customers,
                            COUNT(CASE WHEN balance > 0 THEN 1 END) as customers_with_wallet,
                            COUNT(CASE WHEN balance < 0 THEN 1 END) as customers_with_debt,
                            COALESCE(SUM(CASE WHEN balance > 0 THEN balance ELSE 0 END), 0) as total_wallet_balance,
                            COALESCE(SUM(CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END), 0) as total_debt_balance,
                            COALESCE(SUM(balance), 0) as net_balance
                          FROM customers WHERE id > 1";
    }
    $summary = $conn->query($summary_query)->fetch_assoc();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .balance-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
        }
        .creditor { border-left: 4px solid #28a745; }
        .debtor { border-left: 4px solid #dc3545; }
        .neutral { border-left: 4px solid #6c757d; }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">إدارة الرصيد المحفوظ والديون للعملاء</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="index.php" class="btn btn-light btn-lg me-2">
                        <i class="fas fa-users me-2"></i>قائمة العملاء
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-home me-2"></i>الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (!$has_advanced_system): ?>
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> النظام المتقدم للأرصدة غير مفعل. يتم عرض البيانات من النظام القديم.
                <a href="../../apply-balance-update-final.php" class="btn btn-sm btn-warning ms-2">
                    <i class="fas fa-upgrade me-1"></i>تفعيل النظام المتقدم
                </a>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- ملخص الأرصدة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <h3><?php echo number_format($summary['total_customers']); ?></h3>
                    <p class="mb-0">إجمالي العملاء</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <h3><?php echo formatMoney($summary['total_wallet_balance']); ?></h3>
                    <p class="mb-0">إجمالي الرصيد المحفوظ</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <h3><?php echo formatMoney($summary['total_debt_balance']); ?></h3>
                    <p class="mb-0">إجمالي الديون</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <h3><?php echo formatMoney($summary['net_balance']); ?></h3>
                    <p class="mb-0">الرصيد الصافي</p>
                </div>
            </div>
        </div>

        <!-- قائمة العملاء -->
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    أرصدة العملاء
                </h5>
                <div>
                    <a href="../../sync-customer-balances.php" class="btn btn-light btn-sm me-2">
                        <i class="fas fa-sync me-1"></i>مزامنة الأرصدة
                    </a>
                    <a href="../../debug-customer-balance.php" class="btn btn-warning btn-sm">
                        <i class="fas fa-bug me-1"></i>تشخيص
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>العميل</th>
                                <th>الهاتف</th>
                                <th>الرصيد المحفوظ</th>
                                <th>الديون</th>
                                <th>الرصيد الصافي</th>
                                <th>الحالة</th>
                                <th>آخر معاملة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($customers_result->num_rows > 0): ?>
                                <?php while ($customer = $customers_result->fetch_assoc()): ?>
                                    <?php 
                                    $status_class = '';
                                    if ($customer['balance_status'] == 'دائن') $status_class = 'creditor';
                                    elseif ($customer['balance_status'] == 'مدين') $status_class = 'debtor';
                                    else $status_class = 'neutral';
                                    ?>
                                    <tr class="<?php echo $status_class; ?>">
                                        <td>
                                            <strong><?php echo htmlspecialchars($customer['name']); ?></strong>
                                            <?php if (isset($customer['sales_count']) && $customer['sales_count'] > 0): ?>
                                                <br><small class="text-muted">
                                                    <?php echo $customer['sales_count']; ?> مبيعة
                                                    <?php if ($customer['calculated_debt'] > 0): ?>
                                                        | دين محسوب: <?php echo number_format($customer['calculated_debt'], 2); ?>
                                                    <?php endif; ?>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($customer['phone'] ?? '-'); ?></td>
                                        <td>
                                            <span class="text-success fw-bold">
                                                <?php echo formatMoney($customer['wallet_balance']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-danger fw-bold">
                                                <?php echo formatMoney($customer['debt_balance']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold <?php echo $customer['net_balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo formatMoney(abs($customer['net_balance'])); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php 
                                                echo $customer['balance_status'] == 'دائن' ? 'bg-success' : 
                                                    ($customer['balance_status'] == 'مدين' ? 'bg-danger' : 'bg-secondary'); 
                                            ?>">
                                                <?php echo $customer['balance_status']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($customer['last_transaction_date']): ?>
                                                <?php echo date('Y-m-d', strtotime($customer['last_transaction_date'])); ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-primary btn-sm" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#balanceModal"
                                                        onclick="openBalanceModal(<?php echo $customer['id']; ?>, '<?php echo htmlspecialchars($customer['name']); ?>', <?php echo $customer['wallet_balance']; ?>, <?php echo $customer['debt_balance']; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <a href="view.php?id=<?php echo $customer['id']; ?>" 
                                                   class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد عملاء مسجلين</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>

    <!-- Modal إدارة الرصيد -->
    <div class="modal fade" id="balanceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-balance-scale me-2"></i>
                        إدارة رصيد العميل: <span id="modal-customer-name"></span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    
                    <!-- معلومات الرصيد الحالي -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h6>الرصيد المحفوظ</h6>
                                    <h4 id="current-wallet">0.00 ريال</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h6>الديون</h6>
                                    <h4 id="current-debt">0.00 ريال</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h6>الرصيد الصافي</h6>
                                    <h4 id="current-net">0.00 ريال</h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج العمليات -->
                    <form method="POST" id="balanceForm">
                        <input type="hidden" id="customer_id" name="customer_id">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="action" class="form-label">نوع العملية</label>
                                <select class="form-select" id="action" name="action" required>
                                    <option value="">اختر العملية</option>
                                    <option value="add_wallet">إضافة رصيد محفوظ</option>
                                    <option value="use_wallet">استخدام رصيد محفوظ</option>
                                    <option value="add_debt">إضافة دين</option>
                                    <option value="pay_debt">دفع دين</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">المبلغ</label>
                                <input type="number" class="form-control" id="amount" name="amount" 
                                       min="0.01" step="0.01" required>
                            </div>
                            
                            <div class="col-md-12 mb-3">
                                <label for="description" class="form-label">الوصف</label>
                                <textarea class="form-control" id="description" name="description" 
                                          rows="3" required></textarea>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-secondary me-md-2" data-bs-dismiss="modal">
                                إلغاء
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>تنفيذ العملية
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function openBalanceModal(customerId, customerName, walletBalance, debtBalance) {
            document.getElementById('customer_id').value = customerId;
            document.getElementById('modal-customer-name').textContent = customerName;
            document.getElementById('current-wallet').textContent = walletBalance.toFixed(2) + ' ريال';
            document.getElementById('current-debt').textContent = debtBalance.toFixed(2) + ' ريال';
            
            const netBalance = walletBalance - debtBalance;
            document.getElementById('current-net').textContent = Math.abs(netBalance).toFixed(2) + ' ريال';
            
            // تغيير لون الرصيد الصافي
            const netCard = document.getElementById('current-net').parentElement.parentElement;
            netCard.className = netBalance >= 0 ? 'card bg-success text-white' : 'card bg-danger text-white';
            
            // مسح النموذج
            document.getElementById('balanceForm').reset();
            document.getElementById('customer_id').value = customerId;
        }
    </script>

</body>
</html>
