<?php
/**
 * اختبار المزامنة التلقائية للأرصدة
 * Test Auto Balance Synchronization
 */

require_once "config/db_config.php";
require_once "includes/functions.php";
require_once "includes/advanced-balance-functions.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "اختبار المزامنة التلقائية";

// معالجة اختبار المزامنة
$test_results = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_sync'])) {
    $customer_id = intval($_POST['customer_id']);
    
    if ($customer_id > 1) {
        try {
            // الحصول على الرصيد قبل المزامنة
            $before_query = "SELECT name, debt_balance, wallet_balance FROM customers WHERE id = ?";
            $before_stmt = $conn->prepare($before_query);
            $before_stmt->bind_param("i", $customer_id);
            $before_stmt->execute();
            $before_data = $before_stmt->get_result()->fetch_assoc();
            
            // تشغيل المزامنة التلقائية
            $sync_result = autoSyncCustomerBalance($customer_id);
            
            // الحصول على الرصيد بعد المزامنة
            $after_query = "SELECT debt_balance, wallet_balance FROM customers WHERE id = ?";
            $after_stmt = $conn->prepare($after_query);
            $after_stmt->bind_param("i", $customer_id);
            $after_stmt->execute();
            $after_data = $after_stmt->get_result()->fetch_assoc();
            
            // حساب إجمالي الديون من المبيعات
            $sales_query = "SELECT 
                           COUNT(*) as sales_count,
                           COALESCE(SUM(final_amount), 0) as total_sales,
                           COALESCE(SUM(paid_amount), 0) as total_paid,
                           COALESCE(SUM(final_amount - paid_amount), 0) as calculated_debt
                           FROM sales 
                           WHERE customer_id = ? AND final_amount > paid_amount";
            $sales_stmt = $conn->prepare($sales_query);
            $sales_stmt->bind_param("i", $customer_id);
            $sales_stmt->execute();
            $sales_data = $sales_stmt->get_result()->fetch_assoc();
            
            $test_results = [
                'success' => $sync_result,
                'customer_name' => $before_data['name'],
                'before' => $before_data,
                'after' => $after_data,
                'sales_data' => $sales_data,
                'debt_changed' => abs($before_data['debt_balance'] - $after_data['debt_balance']) > 0.01
            ];
            
        } catch (Exception $e) {
            $test_results = [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    } else {
        $test_results = [
            'success' => false,
            'error' => 'يرجى اختيار عميل صحيح'
        ];
    }
}

// الحصول على قائمة العملاء
$customers_query = "SELECT id, name, debt_balance, wallet_balance FROM customers WHERE id > 1 ORDER BY name";
$customers_result = $conn->query($customers_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
        }
        .test-result {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .result-success {
            border-left: 5px solid #28a745;
        }
        .result-error {
            border-left: 5px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-primary text-white text-center">
                        <h2><i class="fas fa-sync-alt me-2"></i><?php echo $page_title; ?></h2>
                        <p class="mb-0">اختبار عمل المزامنة التلقائية للأرصدة</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- نموذج الاختبار -->
                        <form method="POST" class="mb-4">
                            <div class="row">
                                <div class="col-md-8">
                                    <label for="customer_id" class="form-label">اختر العميل للاختبار</label>
                                    <select class="form-select" id="customer_id" name="customer_id" required>
                                        <option value="">-- اختر العميل --</option>
                                        <?php while ($customer = $customers_result->fetch_assoc()): ?>
                                            <option value="<?php echo $customer['id']; ?>">
                                                <?php echo htmlspecialchars($customer['name']); ?> 
                                                (دين: <?php echo number_format($customer['debt_balance'], 2); ?> - 
                                                 رصيد: <?php echo number_format($customer['wallet_balance'], 2); ?>)
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="submit" name="test_sync" class="btn btn-success w-100">
                                        <i class="fas fa-play me-2"></i>تشغيل الاختبار
                                    </button>
                                </div>
                            </div>
                        </form>
                        
                        <!-- نتائج الاختبار -->
                        <?php if (!empty($test_results)): ?>
                            <div class="test-result <?php echo $test_results['success'] ? 'result-success' : 'result-error'; ?>">
                                <?php if ($test_results['success']): ?>
                                    <h4 class="text-success"><i class="fas fa-check-circle me-2"></i>نتائج الاختبار</h4>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>معلومات العميل:</h6>
                                            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($test_results['customer_name']); ?></p>
                                            
                                            <h6>الرصيد قبل المزامنة:</h6>
                                            <ul>
                                                <li>رصيد الديون: <?php echo number_format($test_results['before']['debt_balance'], 2); ?> ريال</li>
                                                <li>الرصيد المحفوظ: <?php echo number_format($test_results['before']['wallet_balance'], 2); ?> ريال</li>
                                            </ul>
                                            
                                            <h6>الرصيد بعد المزامنة:</h6>
                                            <ul>
                                                <li>رصيد الديون: <?php echo number_format($test_results['after']['debt_balance'], 2); ?> ريال</li>
                                                <li>الرصيد المحفوظ: <?php echo number_format($test_results['after']['wallet_balance'], 2); ?> ريال</li>
                                            </ul>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <h6>بيانات المبيعات:</h6>
                                            <ul>
                                                <li>عدد المبيعات المعلقة: <?php echo $test_results['sales_data']['sales_count']; ?></li>
                                                <li>إجمالي المبيعات: <?php echo number_format($test_results['sales_data']['total_sales'], 2); ?> ريال</li>
                                                <li>إجمالي المدفوع: <?php echo number_format($test_results['sales_data']['total_paid'], 2); ?> ريال</li>
                                                <li>الديون المحسوبة: <?php echo number_format($test_results['sales_data']['calculated_debt'], 2); ?> ريال</li>
                                            </ul>
                                            
                                            <div class="alert <?php echo $test_results['debt_changed'] ? 'alert-warning' : 'alert-success'; ?> mt-3">
                                                <?php if ($test_results['debt_changed']): ?>
                                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                                    تم تحديث الرصيد - كان هناك عدم تطابق
                                                <?php else: ?>
                                                    <i class="fas fa-check me-2"></i>
                                                    الرصيد متزامن بالفعل - لا حاجة للتحديث
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                <?php else: ?>
                                    <h4 class="text-danger"><i class="fas fa-times-circle me-2"></i>خطأ في الاختبار</h4>
                                    <p class="text-danger"><?php echo htmlspecialchars($test_results['error']); ?></p>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- معلومات إضافية -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>كيف تعمل المزامنة التلقائية:</h6>
                            <ol>
                                <li>تحسب إجمالي الديون من جميع المبيعات غير المدفوعة بالكامل</li>
                                <li>تقارن هذا المبلغ مع رصيد الديون المسجل في جدول العملاء</li>
                                <li>تحدث رصيد الديون في جدول العملاء ليطابق المحسوب من المبيعات</li>
                                <li>تحدث تاريخ آخر معاملة للعميل</li>
                            </ol>
                        </div>
                        
                        <div class="text-center">
                            <a href="balance-management-center.php" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-2"></i>العودة لمركز إدارة الأرصدة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
