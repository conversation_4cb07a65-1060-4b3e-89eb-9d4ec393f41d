<?php
/**
 * مزامنة أرصدة العملاء بين المبيعات وجدول العملاء
 * Sync Customer Balances Between Sales and Customers Table
 */

// استدعاء ملفات الإعدادات
require_once "config/db_config.php";
require_once "includes/functions.php";
require_once "includes/advanced-balance-functions.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "مزامنة أرصدة العملاء";
$sync_results = [];
$errors = [];

// معالجة المزامنة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['sync_balances'])) {
    try {
        $conn->begin_transaction();
        
        // التحقق من وجود الأعمدة المتقدمة
        $check_advanced = $conn->query("SHOW COLUMNS FROM customers LIKE 'debt_balance'");
        $has_advanced_system = $check_advanced->num_rows > 0;
        
        if (!$has_advanced_system) {
            // إضافة الأعمدة المتقدمة إذا لم تكن موجودة
            $add_columns = [
                "ALTER TABLE customers ADD COLUMN IF NOT EXISTS wallet_balance DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ'",
                "ALTER TABLE customers ADD COLUMN IF NOT EXISTS debt_balance DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون'",
                "ALTER TABLE customers ADD COLUMN IF NOT EXISTS credit_limit DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'حد الائتمان'",
                "ALTER TABLE customers ADD COLUMN IF NOT EXISTS last_transaction_date DATETIME NULL COMMENT 'تاريخ آخر معاملة'"
            ];
            
            foreach ($add_columns as $sql) {
                try {
                    $conn->query($sql);
                } catch (Exception $e) {
                    // تجاهل الأخطاء إذا كانت الأعمدة موجودة بالفعل
                }
            }
            $sync_results[] = "تم إضافة أعمدة النظام المتقدم";
        }
        
        // حساب الأرصدة من المبيعات لكل عميل
        $customers_sales_query = "
            SELECT 
                c.id,
                c.name,
                c.balance as old_balance,
                COALESCE(c.wallet_balance, 0) as current_wallet,
                COALESCE(c.debt_balance, 0) as current_debt,
                COALESCE(SUM(s.final_amount), 0) as total_sales,
                COALESCE(SUM(s.paid_amount), 0) as total_paid,
                COALESCE(SUM(s.final_amount - s.paid_amount), 0) as calculated_debt,
                COUNT(s.id) as sales_count,
                MAX(s.sale_date) as last_sale_date
            FROM customers c
            LEFT JOIN sales s ON c.id = s.customer_id
            WHERE c.id > 1
            GROUP BY c.id, c.name, c.balance, c.wallet_balance, c.debt_balance
            HAVING calculated_debt > 0.01 OR current_debt != calculated_debt
            ORDER BY c.name
        ";
        
        $customers_result = $conn->query($customers_sales_query);
        $synced_customers = 0;
        
        while ($customer = $customers_result->fetch_assoc()) {
            $customer_id = $customer['id'];
            $calculated_debt = $customer['calculated_debt'];
            $current_debt = $customer['current_debt'];
            $last_sale_date = $customer['last_sale_date'];
            
            // تحديث رصيد الدين إذا كان مختلف
            if (abs($calculated_debt - $current_debt) > 0.01) {
                $update_query = "UPDATE customers SET 
                               debt_balance = ?,
                               last_transaction_date = ?
                               WHERE id = ?";
                
                $stmt = $conn->prepare($update_query);
                $stmt->bind_param("dsi", $calculated_debt, $last_sale_date, $customer_id);
                $stmt->execute();
                
                $sync_results[] = "تم تحديث رصيد العميل: {$customer['name']} - الدين من {$current_debt} إلى {$calculated_debt}";
                $synced_customers++;
                
                // تسجيل المعاملة في balance_transactions إذا كان الجدول موجود
                $check_balance_table = $conn->query("SHOW TABLES LIKE 'balance_transactions'");
                if ($check_balance_table->num_rows > 0) {
                    $balance_diff = $calculated_debt - $current_debt;
                    
                    if (abs($balance_diff) > 0.01) {
                        $transaction_query = "INSERT INTO balance_transactions 
                                            (entity_type, entity_id, transaction_type, amount,
                                             wallet_balance_before, wallet_balance_after,
                                             debt_balance_before, debt_balance_after,
                                             reference_type, reference_id, description, user_id, created_at)
                                            VALUES 
                                            ('customer', ?, 'sync_adjustment', ?,
                                             ?, ?, ?, ?,
                                             'sync', 0, ?, ?, NOW())";
                        
                        $trans_stmt = $conn->prepare($transaction_query);
                        $description = "مزامنة رصيد من المبيعات - تعديل: " . number_format($balance_diff, 2);
                        $user_id = $_SESSION['user_id'] ?? 1;
                        $wallet_balance = $customer['current_wallet'];
                        
                        $trans_stmt->bind_param("iddddsis", 
                            $customer_id, abs($balance_diff),
                            $wallet_balance, $wallet_balance,
                            $current_debt, $calculated_debt,
                            $description, $user_id
                        );
                        $trans_stmt->execute();
                    }
                }
            }
        }
        
        // تحديث المبيعات لتسجيل أن الديون تم تسجيلها
        $update_sales_query = "UPDATE sales SET 
                              debt_amount = (final_amount - paid_amount)
                              WHERE customer_id > 1 
                              AND (final_amount - paid_amount) > 0.01
                              AND (debt_amount IS NULL OR debt_amount = 0)";
        $conn->query($update_sales_query);
        $affected_sales = $conn->affected_rows;
        
        if ($affected_sales > 0) {
            $sync_results[] = "تم تحديث {$affected_sales} مبيعة لتسجيل الديون";
        }
        
        // إحصائيات نهائية
        $sync_results[] = "تم مزامنة {$synced_customers} عميل";
        
        if ($synced_customers == 0) {
            $sync_results[] = "جميع الأرصدة متزامنة بالفعل";
        }
        
        $conn->commit();
        
    } catch (Exception $e) {
        $conn->rollback();
        $errors[] = "خطأ في المزامنة: " . $e->getMessage();
    }
}

// فحص حالة التزامن
$sync_status = [];

try {
    // فحص وجود النظام المتقدم
    $check_advanced = $conn->query("SHOW COLUMNS FROM customers LIKE 'debt_balance'");
    $sync_status['has_advanced_system'] = $check_advanced->num_rows > 0;
    
    // فحص العملاء الذين يحتاجون مزامنة
    $out_of_sync_query = "
        SELECT 
            c.id,
            c.name,
            COALESCE(c.debt_balance, 0) as current_debt,
            COALESCE(SUM(s.final_amount - s.paid_amount), 0) as calculated_debt,
            COUNT(s.id) as sales_count
        FROM customers c
        LEFT JOIN sales s ON c.id = s.customer_id AND s.final_amount > s.paid_amount
        WHERE c.id > 1
        GROUP BY c.id, c.name, c.debt_balance
        HAVING ABS(COALESCE(c.debt_balance, 0) - COALESCE(SUM(s.final_amount - s.paid_amount), 0)) > 0.01
        ORDER BY c.name
    ";
    
    $out_of_sync_result = $conn->query($out_of_sync_query);
    $sync_status['out_of_sync_customers'] = [];
    
    while ($customer = $out_of_sync_result->fetch_assoc()) {
        $sync_status['out_of_sync_customers'][] = $customer;
    }
    
    // إحصائيات عامة
    $stats_query = "SELECT 
                    COUNT(*) as total_customers,
                    SUM(CASE WHEN COALESCE(debt_balance, 0) > 0 THEN 1 ELSE 0 END) as customers_with_debt,
                    SUM(COALESCE(debt_balance, 0)) as total_debt_balance,
                    (SELECT COUNT(*) FROM sales WHERE customer_id > 1 AND final_amount > paid_amount) as sales_with_debt
                    FROM customers WHERE id > 1";
    $stats_result = $conn->query($stats_query);
    $sync_status['stats'] = $stats_result->fetch_assoc();
    
} catch (Exception $e) {
    $errors[] = "خطأ في فحص حالة التزامن: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .sync-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #28a745;
        }
        .out-of-sync-item {
            background: #fff3cd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #ffc107;
        }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-sync-alt me-3"></i>
                مزامنة أرصدة العملاء
            </h1>
            <p class="lead">ربط الأرصدة بين المبيعات وجدول العملاء</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($sync_results)): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>نتائج المزامنة:</h5>
                <ul class="mb-0">
                    <?php foreach ($sync_results as $result): ?>
                        <li><?php echo htmlspecialchars($result); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle me-2"></i>أخطاء:</h5>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <h3 class="text-primary"><?php echo $sync_status['stats']['total_customers']; ?></h3>
                    <small>إجمالي العملاء</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <h3 class="text-danger"><?php echo $sync_status['stats']['customers_with_debt']; ?></h3>
                    <small>عملاء لهم ديون</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <h3 class="text-warning"><?php echo count($sync_status['out_of_sync_customers']); ?></h3>
                    <small>يحتاجون مزامنة</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <h3 class="text-info"><?php echo number_format($sync_status['stats']['total_debt_balance'], 0); ?></h3>
                    <small>إجمالي الديون</small>
                </div>
            </div>
        </div>

        <!-- حالة النظام -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-chart-line me-2"></i>حالة التزامن</h3>
            </div>
            <div class="card-body">
                
                <div class="sync-item <?php echo $sync_status['has_advanced_system'] ? '' : 'out-of-sync-item'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $sync_status['has_advanced_system'] ? 'check' : 'exclamation-triangle'; ?> me-2"></i>
                        نظام الأرصدة المتقدم
                    </h6>
                    <p class="mb-0">
                        <?php if ($sync_status['has_advanced_system']): ?>
                            مفعل - يدعم الأرصدة المنفصلة والديون
                        <?php else: ?>
                            غير مفعل - سيتم تفعيله تلقائياً عند المزامنة
                        <?php endif; ?>
                    </p>
                </div>
                
                <?php if (count($sync_status['out_of_sync_customers']) == 0): ?>
                    <div class="sync-item">
                        <h6><i class="fas fa-check-circle text-success me-2"></i>جميع الأرصدة متزامنة</h6>
                        <p class="mb-0">لا توجد أرصدة تحتاج مزامنة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- العملاء الذين يحتاجون مزامنة -->
        <?php if (!empty($sync_status['out_of_sync_customers'])): ?>
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h3><i class="fas fa-exclamation-triangle me-2"></i>العملاء الذين يحتاجون مزامنة</h3>
                </div>
                <div class="card-body">
                    
                    <p class="text-muted">هؤلاء العملاء لديهم اختلاف بين الأرصدة المسجلة والمحسوبة من المبيعات:</p>
                    
                    <?php foreach ($sync_status['out_of_sync_customers'] as $customer): ?>
                        <div class="out-of-sync-item">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <strong><?php echo htmlspecialchars($customer['name']); ?></strong>
                                </div>
                                <div class="col-md-3">
                                    <small>الرصيد المسجل: <?php echo number_format($customer['current_debt'], 2); ?> ريال</small>
                                </div>
                                <div class="col-md-3">
                                    <small>الرصيد المحسوب: <?php echo number_format($customer['calculated_debt'], 2); ?> ريال</small>
                                </div>
                                <div class="col-md-2">
                                    <span class="badge bg-warning">
                                        فرق: <?php echo number_format(abs($customer['calculated_debt'] - $customer['current_debt']), 2); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        يمكنك مزامنة هذه الأرصدة باستخدام الزر أدناه
                    </div>
                    
                </div>
            </div>
        <?php endif; ?>

        <!-- إجراء المزامنة -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h3><i class="fas fa-sync-alt me-2"></i>تطبيق المزامنة</h3>
            </div>
            <div class="card-body">
                
                <?php if (!empty($sync_status['out_of_sync_customers'])): ?>
                    <p>سيتم تطبيق المزامنة التالية:</p>
                    <ul>
                        <li>تحديث أرصدة العملاء بناءً على المبيعات الفعلية</li>
                        <li>تفعيل النظام المتقدم إذا لم يكن مفعل</li>
                        <li>تسجيل معاملات التعديل في السجلات</li>
                        <li>ربط الديون بالمبيعات الأصلية</li>
                    </ul>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من تطبيق مزامنة الأرصدة؟')">
                        <button type="submit" name="sync_balances" class="btn btn-success btn-lg">
                            <i class="fas fa-sync-alt me-2"></i>
                            مزامنة الأرصدة الآن
                        </button>
                    </form>
                <?php else: ?>
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>جميع الأرصدة متزامنة!</h6>
                        <p class="mb-0">لا توجد حاجة للمزامنة حالياً. جميع أرصدة العملاء متطابقة مع المبيعات.</p>
                    </div>
                    
                    <form method="POST" onsubmit="return confirm('هل تريد إعادة فحص وتطبيق المزامنة؟')">
                        <button type="submit" name="sync_balances" class="btn btn-outline-success btn-lg">
                            <i class="fas fa-redo me-2"></i>
                            إعادة فحص ومزامنة
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>

        <!-- أدوات إضافية -->
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h3><i class="fas fa-tools me-2"></i>أدوات إضافية</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="customer-balance-tracker.php" class="btn btn-info btn-lg w-100 mb-2">
                            <i class="fas fa-chart-line me-2"></i>
                            متتبع الأرصدة
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="fix-customer-balances.php" class="btn btn-warning btn-lg w-100 mb-2">
                            <i class="fas fa-magic me-2"></i>
                            إصلاح الأرصدة
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="pages/customers/index.php" class="btn btn-primary btn-lg w-100 mb-2">
                            <i class="fas fa-users me-2"></i>
                            إدارة العملاء
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="pages/sales/index.php" class="btn btn-success btn-lg w-100 mb-2">
                            <i class="fas fa-receipt me-2"></i>
                            مراجعة المبيعات
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <h2>🔄 مزامنة أرصدة العملاء! 🔄</h2>
            <p class="lead">ربط الأرصدة بين جميع أجزاء النظام</p>
            
            <a href="index.php" class="btn btn-light btn-lg mt-3">
                <i class="fas fa-home me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
