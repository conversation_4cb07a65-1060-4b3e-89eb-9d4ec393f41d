<?php
/**
 * وظائف إدارة الرصيد المتقدمة
 * Advanced Balance Management Functions
 */

/**
 * الحصول على إعدادات نظام الرصيد
 * @param string $key مفتاح الإعداد
 * @param mixed $default القيمة الافتراضية
 * @return mixed قيمة الإعداد
 */
function getBalanceSetting($key, $default = null) {
    global $conn;
    
    $query = "SELECT setting_value FROM balance_settings WHERE setting_key = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['setting_value'];
    }
    
    return $default;
}

/**
 * الحصول على أرصدة العميل أو المورد
 * @param string $entityType نوع الكيان (customer/supplier)
 * @param int $entityId معرف الكيان
 * @return array أرصدة الكيان
 */
function getEntityBalances($entityType, $entityId) {
    global $conn;

    $table = ($entityType === 'customer') ? 'customers' : 'suppliers';

    $query = "SELECT wallet_balance, debt_balance, credit_limit,
                     (wallet_balance - debt_balance) as net_balance
              FROM $table WHERE id = ?";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $entityId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return [
        'wallet_balance' => 0.00,
        'debt_balance' => 0.00,
        'credit_limit' => 0.00,
        'net_balance' => 0.00
    ];
}

/**
 * تحديث رصيد العميل تلقائياً بناءً على المبيعات
 * @param int $customerId معرف العميل
 * @param float $debtAmount مبلغ الدين الجديد
 * @param int $saleId معرف المبيعة
 * @param string $description وصف المعاملة
 * @param int $userId معرف المستخدم
 * @return bool نجح التحديث أم لا
 */
function updateCustomerBalanceFromSale($customerId, $debtAmount, $saleId, $description = '', $userId = 1) {
    global $conn;

    try {
        // التحقق من وجود أعمدة النظام المتقدم
        $check_columns = $conn->query("SHOW COLUMNS FROM customers LIKE 'debt_balance'");
        $has_advanced_system = $check_columns->num_rows > 0;

        if (!$has_advanced_system) {
            // استخدام النظام القديم
            if ($debtAmount > 0) {
                $update_query = "UPDATE customers SET balance = balance + ? WHERE id = ?";
                $stmt = $conn->prepare($update_query);
                $stmt->bind_param("di", $debtAmount, $customerId);
                return $stmt->execute();
            }
            return true;
        }

        // استخدام النظام المتقدم
        if ($debtAmount > 0) {
            // الحصول على الأرصدة الحالية
            $balance_query = "SELECT wallet_balance, debt_balance FROM customers WHERE id = ?";
            $balance_stmt = $conn->prepare($balance_query);
            $balance_stmt->bind_param("i", $customerId);
            $balance_stmt->execute();
            $balance_result = $balance_stmt->get_result();

            if ($balance_result->num_rows > 0) {
                $current_balances = $balance_result->fetch_assoc();
                $wallet_before = $current_balances['wallet_balance'];
                $debt_before = $current_balances['debt_balance'];
                $debt_after = $debt_before + $debtAmount;

                // تحديث رصيد الدين
                $update_query = "UPDATE customers SET
                               debt_balance = debt_balance + ?,
                               last_transaction_date = NOW()
                               WHERE id = ?";
                $stmt = $conn->prepare($update_query);
                $stmt->bind_param("di", $debtAmount, $customerId);
                $stmt->execute();

                // تسجيل المعاملة في balance_transactions إذا كان الجدول موجود
                $check_balance_table = $conn->query("SHOW TABLES LIKE 'balance_transactions'");
                if ($check_balance_table->num_rows > 0) {
                    $transaction_query = "INSERT INTO balance_transactions
                                        (entity_type, entity_id, transaction_type, amount,
                                         wallet_balance_before, wallet_balance_after,
                                         debt_balance_before, debt_balance_after,
                                         reference_type, reference_id, description, user_id, created_at)
                                        VALUES
                                        ('customer', ?, 'debt_add', ?,
                                         ?, ?, ?, ?,
                                         'sale', ?, ?, ?, NOW())";

                    $trans_stmt = $conn->prepare($transaction_query);
                    $final_description = $description ?: "دين من مبيعة";

                    $trans_stmt->bind_param("idddddiis",
                        $customerId, $debtAmount,
                        $wallet_before, $wallet_before,
                        $debt_before, $debt_after,
                        $saleId, $final_description, $userId
                    );
                    $trans_stmt->execute();
                }

                return true;
            }
        }

        return true;

    } catch (Exception $e) {
        error_log("خطأ في تحديث رصيد العميل: " . $e->getMessage());
        return false;
    }
}

/**
 * مزامنة رصيد عميل واحد مع مبيعاته
 * @param int $customerId معرف العميل
 * @return array نتيجة المزامنة
 */
function syncCustomerBalance($customerId) {
    global $conn;

    try {
        // حساب الدين الفعلي من المبيعات
        $sales_query = "SELECT
                       COALESCE(SUM(final_amount - paid_amount), 0) as calculated_debt,
                       COUNT(*) as sales_count,
                       MAX(sale_date) as last_sale_date
                       FROM sales
                       WHERE customer_id = ? AND final_amount > paid_amount";

        $stmt = $conn->prepare($sales_query);
        $stmt->bind_param("i", $customerId);
        $stmt->execute();
        $sales_result = $stmt->get_result()->fetch_assoc();

        $calculated_debt = $sales_result['calculated_debt'];
        $last_sale_date = $sales_result['last_sale_date'];

        // الحصول على الرصيد الحالي
        $customer_query = "SELECT name, COALESCE(debt_balance, 0) as current_debt FROM customers WHERE id = ?";
        $customer_stmt = $conn->prepare($customer_query);
        $customer_stmt->bind_param("i", $customerId);
        $customer_stmt->execute();
        $customer_result = $customer_stmt->get_result();

        if ($customer_result->num_rows > 0) {
            $customer = $customer_result->fetch_assoc();
            $current_debt = $customer['current_debt'];
            $customer_name = $customer['name'];

            // إذا كان هناك اختلاف، قم بالتحديث
            if (abs($calculated_debt - $current_debt) > 0.01) {
                $update_query = "UPDATE customers SET
                               debt_balance = ?,
                               last_transaction_date = ?
                               WHERE id = ?";

                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bind_param("dsi", $calculated_debt, $last_sale_date, $customerId);
                $update_stmt->execute();

                return [
                    'success' => true,
                    'updated' => true,
                    'customer_name' => $customer_name,
                    'old_debt' => $current_debt,
                    'new_debt' => $calculated_debt,
                    'difference' => $calculated_debt - $current_debt
                ];
            } else {
                return [
                    'success' => true,
                    'updated' => false,
                    'customer_name' => $customer_name,
                    'debt' => $current_debt,
                    'message' => 'الرصيد متزامن بالفعل'
                ];
            }
        }

        return ['success' => false, 'message' => 'العميل غير موجود'];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
    }
}

/**
 * إضافة رصيد محفوظ (فكة زائدة)
 * @param string $entityType نوع الكيان
 * @param int $entityId معرف الكيان
 * @param float $amount المبلغ
 * @param string $referenceType نوع المرجع
 * @param int $referenceId معرف المرجع
 * @param string $description الوصف
 * @param int $userId معرف المستخدم
 * @return bool نتيجة العملية
 */
function addWalletBalance($entityType, $entityId, $amount, $referenceType, $referenceId, $description, $userId) {
    global $conn;
    
    try {
        $query = "CALL AddWalletBalance(?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("sidissi", $entityType, $entityId, $amount, $referenceType, $referenceId, $description, $userId);
        $result = $stmt->execute();
        
        return $result;
    } catch (Exception $e) {
        error_log("Error adding wallet balance: " . $e->getMessage());
        return false;
    }
}

/**
 * استخدام الرصيد المحفوظ
 * @param string $entityType نوع الكيان
 * @param int $entityId معرف الكيان
 * @param float $amount المبلغ المطلوب
 * @param string $referenceType نوع المرجع
 * @param int $referenceId معرف المرجع
 * @param string $description الوصف
 * @param int $userId معرف المستخدم
 * @return float المبلغ المستخدم فعلياً
 */
function useWalletBalance($entityType, $entityId, $amount, $referenceType, $referenceId, $description, $userId) {
    global $conn;
    
    try {
        $query = "CALL UseWalletBalance(?, ?, ?, ?, ?, ?, ?, @used_amount)";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("sidissi", $entityType, $entityId, $amount, $referenceType, $referenceId, $description, $userId);
        $stmt->execute();
        
        // الحصول على المبلغ المستخدم
        $result = $conn->query("SELECT @used_amount as used_amount");
        $row = $result->fetch_assoc();
        
        return floatval($row['used_amount']);
    } catch (Exception $e) {
        error_log("Error using wallet balance: " . $e->getMessage());
        return 0.00;
    }
}

/**
 * إضافة دين
 * @param string $entityType نوع الكيان
 * @param int $entityId معرف الكيان
 * @param float $amount المبلغ
 * @param string $referenceType نوع المرجع
 * @param int $referenceId معرف المرجع
 * @param string $description الوصف
 * @param int $userId معرف المستخدم
 * @param bool $autoDeduct الخصم التلقائي من الرصيد المحفوظ
 * @return bool نتيجة العملية
 */
function addDebtBalance($entityType, $entityId, $amount, $referenceType, $referenceId, $description, $userId, $autoDeduct = true) {
    global $conn;
    
    try {
        $query = "CALL AddDebtBalance(?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("sidissii", $entityType, $entityId, $amount, $referenceType, $referenceId, $description, $userId, $autoDeduct);
        $result = $stmt->execute();
        
        return $result;
    } catch (Exception $e) {
        error_log("Error adding debt balance: " . $e->getMessage());
        return false;
    }
}

/**
 * معالجة دفعة متقدمة (مع إدارة الفكة والديون)
 * @param string $entityType نوع الكيان
 * @param int $entityId معرف الكيان
 * @param float $totalAmount إجمالي المبلغ المطلوب
 * @param float $cashPaid المبلغ المدفوع نقداً
 * @param bool $useWallet استخدام الرصيد المحفوظ
 * @param string $referenceType نوع المرجع
 * @param int $referenceId معرف المرجع
 * @param int $userId معرف المستخدم
 * @return array تفاصيل المعاملة
 */
function processAdvancedPayment($entityType, $entityId, $totalAmount, $cashPaid, $useWallet, $referenceType, $referenceId, $userId) {
    global $conn;
    
    $result = [
        'success' => false,
        'cash_paid' => $cashPaid,
        'wallet_used' => 0.00,
        'change_amount' => 0.00,
        'wallet_added' => 0.00,
        'debt_amount' => 0.00,
        'total_paid' => 0.00,
        'remaining' => 0.00,
        'message' => ''
    ];
    
    try {
        $conn->begin_transaction();
        
        // الحصول على الأرصدة الحالية
        $balances = getEntityBalances($entityType, $entityId);
        $walletBalance = floatval($balances['wallet_balance']);
        
        $totalPaid = $cashPaid;
        $remaining = $totalAmount;
        
        // استخدام الرصيد المحفوظ إذا كان مطلوباً ومتاحاً
        if ($useWallet && $walletBalance > 0) {
            $minWalletUsage = floatval(getBalanceSetting('min_wallet_usage', 10.00));
            
            if ($walletBalance >= $minWalletUsage) {
                $walletUsed = useWalletBalance($entityType, $entityId, $remaining, $referenceType, $referenceId, 
                                             "استخدام رصيد محفوظ - $referenceType #$referenceId", $userId);
                
                $result['wallet_used'] = $walletUsed;
                $totalPaid += $walletUsed;
                $remaining -= $walletUsed;
            }
        }
        
        $result['total_paid'] = $totalPaid;
        $result['remaining'] = max(0, $remaining);
        
        // معالجة الفكة أو الدين
        if ($totalPaid > $totalAmount) {
            // هناك فكة زائدة - إضافتها للرصيد المحفوظ
            $changeAmount = $totalPaid - $totalAmount;
            $result['change_amount'] = $changeAmount;
            $result['wallet_added'] = $changeAmount;
            
            addWalletBalance($entityType, $entityId, $changeAmount, $referenceType, $referenceId,
                           "فكة زائدة - $referenceType #$referenceId", $userId);
            
            $result['message'] = "تم إضافة " . formatMoney($changeAmount) . " للرصيد المحفوظ";
            
        } elseif ($remaining > 0) {
            // هناك مبلغ متبقي - تسجيله كدين
            $result['debt_amount'] = $remaining;
            
            $autoDeduct = (bool)getBalanceSetting('auto_debt_deduction', 1);
            addDebtBalance($entityType, $entityId, $remaining, $referenceType, $referenceId,
                          "دين مسجل - $referenceType #$referenceId", $userId, $autoDeduct);
            
            $result['message'] = "تم تسجيل دين بمبلغ " . formatMoney($remaining);
        } else {
            $result['message'] = "تم الدفع بالكامل";
        }
        
        $conn->commit();
        $result['success'] = true;
        
    } catch (Exception $e) {
        $conn->rollback();
        $result['message'] = "خطأ في معالجة الدفعة: " . $e->getMessage();
        error_log("Error processing advanced payment: " . $e->getMessage());
    }
    
    return $result;
}

/**
 * الحصول على تاريخ معاملات الرصيد
 * @param string $entityType نوع الكيان
 * @param int $entityId معرف الكيان
 * @param int $limit عدد السجلات
 * @return array تاريخ المعاملات
 */
function getBalanceTransactionHistory($entityType, $entityId, $limit = 50) {
    global $conn;
    
    $query = "SELECT bt.*, u.name as user_name
              FROM balance_transactions bt
              LEFT JOIN users u ON bt.user_id = u.id
              WHERE bt.entity_type = ? AND bt.entity_id = ?
              ORDER BY bt.created_at DESC
              LIMIT ?";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("sii", $entityType, $entityId, $limit);
    $stmt->execute();
    
    return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
}

/**
 * التحقق من إمكانية استخدام الرصيد المحفوظ
 * @param string $entityType نوع الكيان
 * @param int $entityId معرف الكيان
 * @return array معلومات الرصيد المتاح
 */
function checkWalletUsageEligibility($entityType, $entityId) {
    $balances = getEntityBalances($entityType, $entityId);
    $walletBalance = floatval($balances['wallet_balance']);
    $minWalletUsage = floatval(getBalanceSetting('min_wallet_usage', 10.00));
    
    return [
        'eligible' => $walletBalance >= $minWalletUsage,
        'wallet_balance' => $walletBalance,
        'min_usage' => $minWalletUsage,
        'available_amount' => $walletBalance
    ];
}

/**
 * الحصول على ملخص أرصدة جميع العملاء
 * @return array ملخص الأرصدة
 */
function getCustomersBalanceSummary() {
    global $conn;
    
    $query = "SELECT 
                COUNT(*) as total_customers,
                COUNT(CASE WHEN wallet_balance > 0 THEN 1 END) as customers_with_wallet,
                COUNT(CASE WHEN debt_balance > 0 THEN 1 END) as customers_with_debt,
                COALESCE(SUM(wallet_balance), 0) as total_wallet_balance,
                COALESCE(SUM(debt_balance), 0) as total_debt_balance,
                COALESCE(SUM(wallet_balance - debt_balance), 0) as net_balance
              FROM customers";
    
    $result = $conn->query($query);
    return $result->fetch_assoc();
}

/**
 * الحصول على ملخص أرصدة جميع الموردين
 * @return array ملخص الأرصدة
 */
function getSuppliersBalanceSummary() {
    global $conn;
    
    $query = "SELECT 
                COUNT(*) as total_suppliers,
                COUNT(CASE WHEN wallet_balance > 0 THEN 1 END) as suppliers_with_wallet,
                COUNT(CASE WHEN debt_balance > 0 THEN 1 END) as suppliers_with_debt,
                COALESCE(SUM(wallet_balance), 0) as total_wallet_balance,
                COALESCE(SUM(debt_balance), 0) as total_debt_balance,
                COALESCE(SUM(wallet_balance - debt_balance), 0) as net_balance
              FROM suppliers";
    
    $result = $conn->query($query);
    return $result->fetch_assoc();
}

/**
 * تحديد حالة الرصيد (دائن/مدين/متعادل)
 * @param float $walletBalance الرصيد المحفوظ
 * @param float $debtBalance رصيد الديون
 * @return array معلومات الحالة
 */
function getBalanceStatus($walletBalance, $debtBalance) {
    $netBalance = $walletBalance - $debtBalance;
    
    if ($netBalance > 0) {
        return [
            'status' => 'دائن',
            'class' => 'text-success',
            'icon' => 'fas fa-plus-circle',
            'description' => 'له رصيد محفوظ'
        ];
    } elseif ($netBalance < 0) {
        return [
            'status' => 'مدين',
            'class' => 'text-danger',
            'icon' => 'fas fa-minus-circle',
            'description' => 'عليه مبلغ مستحق'
        ];
    } else {
        return [
            'status' => 'متعادل',
            'class' => 'text-muted',
            'icon' => 'fas fa-equals',
            'description' => 'لا يوجد رصيد مستحق'
        ];
    }
}

/**
 * تنسيق عرض الرصيد
 * @param float $walletBalance الرصيد المحفوظ
 * @param float $debtBalance رصيد الديون
 * @param string $currency العملة
 * @return string النص المنسق
 */
function formatBalanceDisplay($walletBalance, $debtBalance, $currency = 'ريال') {
    $status = getBalanceStatus($walletBalance, $debtBalance);
    $netBalance = abs($walletBalance - $debtBalance);
    
    return sprintf(
        '<span class="%s"><i class="%s me-1"></i>%s: %s %s</span>',
        $status['class'],
        $status['icon'],
        $status['status'],
        number_format($netBalance, 2),
        $currency
    );
}

/**
 * الحصول على العملاء الذين لديهم ديون متأخرة
 * @param int $days عدد الأيام
 * @return array قائمة العملاء
 */
function getOverdueDebtors($days = 30) {
    global $conn;
    
    $query = "SELECT c.*, 
                     DATEDIFF(NOW(), c.last_transaction_date) as days_overdue
              FROM customers c
              WHERE c.debt_balance > 0 
                AND c.last_transaction_date IS NOT NULL
                AND DATEDIFF(NOW(), c.last_transaction_date) > ?
              ORDER BY days_overdue DESC, c.debt_balance DESC";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $days);
    $stmt->execute();
    
    return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
}

/**
 * الحصول على العملاء الذين لديهم رصيد محفوظ كبير
 * @param float $threshold الحد الأدنى
 * @return array قائمة العملاء
 */
function getHighWalletBalanceCustomers($threshold = 100.00) {
    global $conn;
    
    $query = "SELECT * FROM customers 
              WHERE wallet_balance >= ?
              ORDER BY wallet_balance DESC";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("d", $threshold);
    $stmt->execute();
    
    return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
}
?>
