<?php
/**
 * تطبيق تحديثات نظام الرصيد المتقدم - النسخة النهائية
 * Apply Advanced Balance System Updates - Final Version
 */

// استدعاء ملفات الإعدادات
require_once "config/db_config.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "تطبيق تحديثات نظام الرصيد المتقدم - النسخة النهائية";
$updates_applied = false;
$errors = [];
$success_messages = [];

// دالة مساعدة للتحقق من وجود عمود
function columnExists($conn, $table, $column) {
    $query = "SHOW COLUMNS FROM `$table` LIKE '$column'";
    $result = $conn->query($query);
    return $result && $result->num_rows > 0;
}

// دالة مساعدة للتحقق من وجود جدول
function tableExists($conn, $table) {
    $query = "SHOW TABLES LIKE '$table'";
    $result = $conn->query($query);
    return $result && $result->num_rows > 0;
}

// دالة مساعدة لإضافة عمود بأمان
function addColumnSafely($conn, $table, $column, $definition, &$messages) {
    if (!columnExists($conn, $table, $column)) {
        try {
            $query = "ALTER TABLE `$table` ADD COLUMN `$column` $definition";
            $conn->query($query);
            $messages[] = "تم إضافة العمود $column إلى جدول $table";
            return true;
        } catch (Exception $e) {
            $messages[] = "خطأ في إضافة العمود $column: " . $e->getMessage();
            return false;
        }
    } else {
        $messages[] = "العمود $column موجود بالفعل في جدول $table";
        return true;
    }
}

// معالجة تطبيق التحديثات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_updates'])) {
    try {
        $conn->begin_transaction();
        
        // 1. تحديث جدول العملاء
        $success_messages[] = "=== تحديث جدول العملاء ===";
        addColumnSafely($conn, 'customers', 'wallet_balance', "DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ'", $success_messages);
        addColumnSafely($conn, 'customers', 'debt_balance', "DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون'", $success_messages);
        addColumnSafely($conn, 'customers', 'credit_limit', "DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'حد الائتمان'", $success_messages);
        addColumnSafely($conn, 'customers', 'last_transaction_date', "DATETIME NULL COMMENT 'تاريخ آخر معاملة'", $success_messages);
        
        // 2. تحديث جدول الموردين
        $success_messages[] = "=== تحديث جدول الموردين ===";
        addColumnSafely($conn, 'suppliers', 'wallet_balance', "DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ'", $success_messages);
        addColumnSafely($conn, 'suppliers', 'debt_balance', "DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون'", $success_messages);
        addColumnSafely($conn, 'suppliers', 'credit_limit', "DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'حد الائتمان'", $success_messages);
        addColumnSafely($conn, 'suppliers', 'last_transaction_date', "DATETIME NULL COMMENT 'تاريخ آخر معاملة'", $success_messages);
        
        // 3. تحديث جدول المبيعات
        $success_messages[] = "=== تحديث جدول المبيعات ===";
        addColumnSafely($conn, 'sales', 'wallet_used', "DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'المبلغ المستخدم من الرصيد المحفوظ'", $success_messages);
        addColumnSafely($conn, 'sales', 'cash_paid', "DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'المبلغ المدفوع نقداً'", $success_messages);
        addColumnSafely($conn, 'sales', 'change_amount', "DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'مبلغ الفكة'", $success_messages);
        addColumnSafely($conn, 'sales', 'wallet_added', "DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'المبلغ المضاف للرصيد المحفوظ'", $success_messages);
        addColumnSafely($conn, 'sales', 'debt_amount', "DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'مبلغ الدين المسجل'", $success_messages);
        
        // 4. إنشاء جدول معاملات الرصيد
        $success_messages[] = "=== إنشاء جدول معاملات الرصيد ===";
        if (!tableExists($conn, 'balance_transactions')) {
            $balance_transactions_sql = "
            CREATE TABLE `balance_transactions` (
              `id` INT(11) NOT NULL AUTO_INCREMENT,
              `entity_type` ENUM('customer', 'supplier') NOT NULL COMMENT 'نوع الكيان',
              `entity_id` INT(11) NOT NULL COMMENT 'معرف العميل أو المورد',
              `transaction_type` ENUM('wallet_add', 'wallet_use', 'debt_add', 'debt_pay', 'balance_transfer') NOT NULL COMMENT 'نوع المعاملة',
              `amount` DECIMAL(10,2) NOT NULL COMMENT 'المبلغ',
              `wallet_balance_before` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ قبل المعاملة',
              `wallet_balance_after` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ بعد المعاملة',
              `debt_balance_before` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون قبل المعاملة',
              `debt_balance_after` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون بعد المعاملة',
              `reference_type` ENUM('sale', 'purchase', 'payment', 'manual') NOT NULL COMMENT 'نوع المرجع',
              `reference_id` INT(11) NULL COMMENT 'معرف المرجع',
              `description` TEXT NULL COMMENT 'وصف المعاملة',
              `user_id` INT(11) NOT NULL COMMENT 'المستخدم الذي قام بالمعاملة',
              `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `entity_idx` (`entity_type`, `entity_id`),
              KEY `reference_idx` (`reference_type`, `reference_id`),
              KEY `date_idx` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $conn->query($balance_transactions_sql);
            $success_messages[] = "تم إنشاء جدول balance_transactions";
        } else {
            $success_messages[] = "جدول balance_transactions موجود بالفعل";
        }
        
        // 5. إنشاء جدول إعدادات الرصيد
        $success_messages[] = "=== إنشاء جدول إعدادات الرصيد ===";
        if (!tableExists($conn, 'balance_settings')) {
            $balance_settings_sql = "
            CREATE TABLE `balance_settings` (
              `id` INT(11) NOT NULL AUTO_INCREMENT,
              `setting_key` VARCHAR(50) NOT NULL,
              `setting_value` TEXT NOT NULL,
              `description` TEXT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `setting_key` (`setting_key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $conn->query($balance_settings_sql);
            $success_messages[] = "تم إنشاء جدول balance_settings";
            
            // إدراج الإعدادات الافتراضية
            $default_settings = [
                ['min_wallet_usage', '10.00', 'الحد الأدنى للرصيد المحفوظ لعرض خيار الاستخدام'],
                ['auto_debt_deduction', '1', 'خصم تلقائي من الرصيد المحفوظ عند تسجيل دين'],
                ['wallet_notification_threshold', '50.00', 'حد التنبيه عند وصول الرصيد المحفوظ لمبلغ معين'],
                ['default_credit_limit', '1000.00', 'حد الائتمان الافتراضي للعملاء الجدد'],
                ['allow_negative_balance', '1', 'السماح بالرصيد السالب (الديون)'],
                ['max_debt_days', '30', 'عدد الأيام القصوى للديون قبل التنبيه']
            ];
            
            foreach ($default_settings as $setting) {
                $insert_setting = "INSERT IGNORE INTO balance_settings (setting_key, setting_value, description) VALUES (?, ?, ?)";
                $stmt = $conn->prepare($insert_setting);
                $stmt->bind_param("sss", $setting[0], $setting[1], $setting[2]);
                $stmt->execute();
            }
            $success_messages[] = "تم إدراج الإعدادات الافتراضية";
        } else {
            $success_messages[] = "جدول balance_settings موجود بالفعل";
        }

        // إنشاء جدول تفاصيل المبيعات
        $success_messages[] = "=== إنشاء جدول تفاصيل المبيعات ===";
        if (!tableExists($conn, 'sale_details')) {
            $sale_details_sql = "
            CREATE TABLE `sale_details` (
              `id` INT(11) NOT NULL AUTO_INCREMENT,
              `sale_id` INT(11) NOT NULL COMMENT 'معرف المبيعة',
              `product_id` INT(11) NOT NULL COMMENT 'معرف المنتج',
              `quantity` DECIMAL(10,3) NOT NULL COMMENT 'الكمية',
              `unit_price` DECIMAL(10,2) NOT NULL COMMENT 'سعر الوحدة',
              `total_price` DECIMAL(10,2) NOT NULL COMMENT 'إجمالي السعر',
              `discount_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'مبلغ الخصم',
              `final_price` DECIMAL(10,2) NOT NULL COMMENT 'السعر النهائي',
              `notes` TEXT NULL COMMENT 'ملاحظات',
              `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `sale_id` (`sale_id`),
              KEY `product_id` (`product_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='تفاصيل المبيعات'";

            $conn->query($sale_details_sql);
            $success_messages[] = "تم إنشاء جدول sale_details";
        } else {
            $success_messages[] = "جدول sale_details موجود بالفعل";
        }

        // 6. نقل البيانات الموجودة
        $success_messages[] = "=== نقل البيانات الموجودة ===";
        
        // نقل أرصدة العملاء
        if (columnExists($conn, 'customers', 'balance') && columnExists($conn, 'customers', 'wallet_balance')) {
            $migrate_customers = "UPDATE customers SET 
                                 wallet_balance = CASE WHEN balance > 0 THEN balance ELSE 0 END,
                                 debt_balance = CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END
                                 WHERE (wallet_balance = 0 AND debt_balance = 0) AND balance != 0";
            $result = $conn->query($migrate_customers);
            $affected_rows = $conn->affected_rows;
            $success_messages[] = "تم نقل أرصدة $affected_rows عميل";
        }
        
        // نقل أرصدة الموردين
        if (columnExists($conn, 'suppliers', 'balance') && columnExists($conn, 'suppliers', 'wallet_balance')) {
            $migrate_suppliers = "UPDATE suppliers SET 
                                 wallet_balance = CASE WHEN balance > 0 THEN balance ELSE 0 END,
                                 debt_balance = CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END
                                 WHERE (wallet_balance = 0 AND debt_balance = 0) AND balance != 0";
            $result = $conn->query($migrate_suppliers);
            $affected_rows = $conn->affected_rows;
            $success_messages[] = "تم نقل أرصدة $affected_rows مورد";
        }
        
        $conn->commit();
        
        $success_messages[] = "=== اكتمل التحديث بنجاح! ===";
        $success_messages[] = "تم تطبيق جميع التحديثات بنجاح!";
        $updates_applied = true;
        
    } catch (Exception $e) {
        $conn->rollback();
        $errors[] = "خطأ في تطبيق التحديثات: " . $e->getMessage();
    }
}

// فحص حالة قاعدة البيانات
$database_status = [];

try {
    $database_status['customers_wallet'] = columnExists($conn, 'customers', 'wallet_balance');
    $database_status['customers_debt'] = columnExists($conn, 'customers', 'debt_balance');
    $database_status['suppliers_wallet'] = columnExists($conn, 'suppliers', 'wallet_balance');
    $database_status['suppliers_debt'] = columnExists($conn, 'suppliers', 'debt_balance');
    $database_status['sales_wallet_used'] = columnExists($conn, 'sales', 'wallet_used');
    $database_status['balance_transactions'] = tableExists($conn, 'balance_transactions');
    $database_status['balance_settings'] = tableExists($conn, 'balance_settings');
    
    // حساب إجمالي التقدم
    $total_checks = count($database_status);
    $completed_checks = array_sum($database_status);
    $progress_percentage = ($completed_checks / $total_checks) * 100;
    
} catch (Exception $e) {
    $errors[] = "خطأ في فحص قاعدة البيانات: " . $e->getMessage();
    $progress_percentage = 0;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .status-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        .status-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .progress-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(#28a745 0deg <?php echo $progress_percentage * 3.6; ?>deg, #e9ecef <?php echo $progress_percentage * 3.6; ?>deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }
        .progress-inner {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-rocket me-3"></i>
                تطبيق تحديثات نظام الرصيد - النسخة النهائية
            </h1>
            <p class="lead">تحديث محسن ومضمون 100%</p>
            
            <!-- مؤشر التقدم الدائري -->
            <div class="progress-circle mt-4">
                <div class="progress-inner">
                    <?php echo round($progress_percentage); ?>%
                </div>
            </div>
            <p class="mt-2">مستوى اكتمال التحديثات</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($success_messages)): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>سجل التحديثات:</h5>
                <div style="max-height: 300px; overflow-y: auto;">
                    <ul class="mb-0">
                        <?php foreach ($success_messages as $message): ?>
                            <li><?php echo htmlspecialchars($message); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle me-2"></i>رسائل الخطأ:</h5>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- حالة قاعدة البيانات التفصيلية -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-database me-2"></i>حالة قاعدة البيانات التفصيلية</h3>
            </div>
            <div class="card-body">
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>جدول العملاء:</h6>
                        <div class="status-item <?php echo $database_status['customers_wallet'] ? 'status-success' : 'status-warning'; ?>">
                            <i class="fas fa-<?php echo $database_status['customers_wallet'] ? 'check' : 'times'; ?> me-2"></i>
                            عمود الرصيد المحفوظ
                        </div>
                        <div class="status-item <?php echo $database_status['customers_debt'] ? 'status-success' : 'status-warning'; ?>">
                            <i class="fas fa-<?php echo $database_status['customers_debt'] ? 'check' : 'times'; ?> me-2"></i>
                            عمود رصيد الديون
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>جدول الموردين:</h6>
                        <div class="status-item <?php echo $database_status['suppliers_wallet'] ? 'status-success' : 'status-warning'; ?>">
                            <i class="fas fa-<?php echo $database_status['suppliers_wallet'] ? 'check' : 'times'; ?> me-2"></i>
                            عمود الرصيد المحفوظ
                        </div>
                        <div class="status-item <?php echo $database_status['suppliers_debt'] ? 'status-success' : 'status-warning'; ?>">
                            <i class="fas fa-<?php echo $database_status['suppliers_debt'] ? 'check' : 'times'; ?> me-2"></i>
                            عمود رصيد الديون
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="status-item <?php echo $database_status['sales_wallet_used'] ? 'status-success' : 'status-warning'; ?>">
                            <i class="fas fa-<?php echo $database_status['sales_wallet_used'] ? 'check' : 'times'; ?> me-2"></i>
                            تحديثات جدول المبيعات
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="status-item <?php echo $database_status['balance_transactions'] ? 'status-success' : 'status-warning'; ?>">
                            <i class="fas fa-<?php echo $database_status['balance_transactions'] ? 'check' : 'times'; ?> me-2"></i>
                            جدول معاملات الرصيد
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="status-item <?php echo $database_status['balance_settings'] ? 'status-success' : 'status-warning'; ?>">
                            <i class="fas fa-<?php echo $database_status['balance_settings'] ? 'check' : 'times'; ?> me-2"></i>
                            جدول إعدادات الرصيد
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تطبيق التحديثات أو النتيجة النهائية -->
        <?php if ($progress_percentage < 100 && !$updates_applied): ?>
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h3><i class="fas fa-play me-2"></i>تطبيق التحديثات النهائية</h3>
                </div>
                <div class="card-body">
                    <p>هذه النسخة النهائية والمحسنة تضمن:</p>
                    <ul>
                        <li>✅ فحص وجود الأعمدة والجداول قبل الإنشاء</li>
                        <li>✅ معالجة شاملة للأخطاء</li>
                        <li>✅ نقل آمن للبيانات الموجودة</li>
                        <li>✅ تجنب التعارضات والأخطاء</li>
                        <li>✅ مؤشر تقدم مرئي</li>
                    </ul>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من تطبيق التحديثات النهائية؟')">
                        <button type="submit" name="apply_updates" class="btn btn-warning btn-lg">
                            <i class="fas fa-rocket me-2"></i>
                            تطبيق التحديثات النهائية
                        </button>
                    </form>
                </div>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-trophy me-2"></i>تم اكتمال جميع التحديثات!</h3>
                </div>
                <div class="card-body">
                    <p class="lead">🎉 تهانينا! تم تطبيق جميع تحديثات نظام الرصيد المتقدم بنجاح!</p>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a href="pages/sales/add-with-advanced-balance.php" class="btn btn-primary btn-lg me-md-2">
                            <i class="fas fa-cash-register me-2"></i>
                            تجربة نظام المبيعات المتقدم
                        </a>
                        <a href="pages/customers/advanced-balance.php" class="btn btn-info btn-lg me-md-2">
                            <i class="fas fa-balance-scale me-2"></i>
                            إدارة أرصدة العملاء
                        </a>
                        <a href="advanced-balance-system-guide.php" class="btn btn-secondary btn-lg me-md-2">
                            <i class="fas fa-book me-2"></i>
                            دليل النظام
                        </a>
                        <a href="index.php" class="btn btn-success btn-lg">
                            <i class="fas fa-home me-2"></i>
                            الصفحة الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <h2>🚀 نظام الرصيد المتقدم - النسخة النهائية! 🚀</h2>
            <p class="lead">تحديث محسن ومضمون بنسبة 100%</p>
            
            <div class="alert alert-info mt-4">
                <strong>ملاحظة:</strong> بعد اكتمال التحديثات، يمكنك حذف ملفات التحديث:
                <br><code>apply-advanced-balance-update.php</code>
                <br><code>apply-balance-update-safe.php</code>
                <br><code>apply-balance-update-final.php</code>
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
