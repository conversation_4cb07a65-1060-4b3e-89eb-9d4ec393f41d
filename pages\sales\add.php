<?php
/**
 * صفحة إضافة مبيعة جديدة
 * Add New Sale Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";
require_once "../../includes/advanced-balance-functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "مبيعة جديدة";
$page_icon = "fas fa-plus-circle";

// معالجة إضافة المبيعة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_sale'])) {
    try {
        $conn->begin_transaction();
        
        // بيانات المبيعة
        $customer_id = clean($conn, $_POST['customer_id']);
        $sale_date = clean($conn, $_POST['sale_date']);
        $total_amount = floatval($_POST['total_amount']);
        $discount_amount = floatval($_POST['discount_amount']);
        $final_amount = floatval($_POST['final_amount']);
        $cash_paid = floatval($_POST['cash_paid'] ?? $_POST['paid_amount'] ?? 0);
        $wallet_used = floatval($_POST['wallet_used'] ?? 0);
        $payment_method = clean($conn, $_POST['payment_method']);
        $notes = clean($conn, $_POST['notes']);

        // حساب إجمالي المدفوع
        $paid_amount = $cash_paid + $wallet_used;
        
        // توليد رقم المبيعة
        $sale_number = generateSaleNumber($conn);
        
        // إدراج المبيعة مع الأعمدة الجديدة
        $insert_query = "INSERT INTO sales (sale_number, customer_id, user_id, sale_date, total_amount, discount_amount,
                                          final_amount, paid_amount, payment_method, notes, cash_paid, wallet_used,
                                          change_amount, wallet_added, debt_amount)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $change_amount = 0;
        $wallet_added = 0;
        $debt_amount = 0;

        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param("siisddddssddddd", $sale_number, $customer_id, $_SESSION['user_id'], $sale_date,
                         $total_amount, $discount_amount, $final_amount, $paid_amount, $payment_method, $notes,
                         $cash_paid, $wallet_used, $change_amount, $wallet_added, $debt_amount);
        $stmt->execute();
        
        $sale_id = $conn->insert_id;
        
        // إدراج أصناف المبيعة
        if (isset($_POST['products']) && is_array($_POST['products'])) {
            foreach ($_POST['products'] as $product_data) {
                if (!empty($product_data['product_id']) && isset($product_data['quantity']) && $product_data['quantity'] > 0) {
                    $product_id = intval($product_data['product_id']);
                    $quantity = floatval($product_data['quantity']);
                    $unit_price = floatval($product_data['unit_price']);
                    $total_price = $quantity * $unit_price;

                    // إدراج تفاصيل المبيعة
                    $item_query = "INSERT INTO sale_details (sale_id, product_id, quantity, unit_price, total_price, final_price) VALUES (?, ?, ?, ?, ?, ?)";
                    $item_stmt = $conn->prepare($item_query);
                    $item_stmt->bind_param("iidddd", $sale_id, $product_id, $quantity, $unit_price, $total_price, $total_price);
                    $item_stmt->execute();

                    // تحديث المخزون
                    $update_stock = "UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?";
                    $stock_stmt = $conn->prepare($update_stock);
                    $stock_stmt->bind_param("di", $quantity, $product_id);
                    $stock_stmt->execute();
                }
            }
        }
        
        // تحديث الخزينة
        if ($paid_amount > 0) {
            $description = "مبيعة رقم: $sale_number";
            addTreasuryTransaction('sales', $sale_id, $paid_amount, $description, $_SESSION['user_id'], $sale_date);
        }
        
        // معالجة الدفع المتقدم
        if ($customer_id > 1 && function_exists('processAdvancedPayment')) {
            $use_wallet = $wallet_used > 0 ? true : false;

            $paymentResult = processAdvancedPayment(
                'customer',
                $customer_id,
                $final_amount,
                $cash_paid,
                $use_wallet,
                'sale',
                $sale_id,
                $_SESSION['user_id']
            );

            // تحديث بيانات المبيعة بنتائج الدفع المتقدم
            $update_payment = "UPDATE sales SET
                              cash_paid = ?,
                              wallet_used = ?,
                              change_amount = ?,
                              wallet_added = ?,
                              debt_amount = ?,
                              paid_amount = ?
                              WHERE id = ?";

            $total_paid = $paymentResult['cash_paid'] + $paymentResult['wallet_used'];
            $payment_stmt = $conn->prepare($update_payment);
            $payment_stmt->bind_param("ddddddi",
                $paymentResult['cash_paid'],
                $paymentResult['wallet_used'],
                $paymentResult['change_amount'],
                $paymentResult['wallet_added'],
                $paymentResult['debt_amount'],
                $total_paid,
                $sale_id
            );
            $payment_stmt->execute();
        } else {
            // النظام القديم - تحديث رصيد العميل (إذا كان هناك مبلغ متبقي)
            $remaining_amount = $final_amount - $paid_amount;
            if ($remaining_amount > 0 && $customer_id > 1) {
                // استخدام الدالة الجديدة للتحديث التلقائي
                $debt_description = "دين من مبيعة رقم: $sale_number";
                updateCustomerBalanceFromSale($customer_id, $remaining_amount, $sale_id, $debt_description, $_SESSION['user_id']);
            }
        }
        
        $conn->commit();
        $_SESSION['success_message'] = "تم إضافة المبيعة بنجاح برقم: $sale_number";
        header("Location: view.php?id=$sale_id");
        exit();
        
    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['error_message'] = "خطأ في إضافة المبيعة: " . $e->getMessage();
    }
}

// الحصول على قائمة العملاء
$customers_query = "SELECT id, name, phone FROM customers ORDER BY name";
$customers_result = $conn->query($customers_query);

// الحصول على قائمة المنتجات
$products_query = "SELECT id, name, selling_price, stock_quantity, unit FROM products WHERE is_active = 1 ORDER BY name";
$products_result = $conn->query($products_query);

// دالة توليد رقم المبيعة موجودة في includes/functions.php
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .form-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        .products-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
        }
        .summary-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
        }
        .btn-add-product {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-add-product:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }
        .select2-container--default .select2-selection--single {
            height: 38px;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 36px;
            padding-right: 12px;
        }

        /* تنسيق النظام المتقدم */
        .payment-section {
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .alert-sm {
            padding: 8px 12px;
            font-size: 0.875rem;
            margin-bottom: 8px;
        }

        #customer-balance-info {
            background: rgba(23, 162, 184, 0.1);
            border: 1px solid rgba(23, 162, 184, 0.3);
            color: #17a2b8;
        }

        .input-group .btn-outline-light {
            border-color: rgba(255,255,255,0.3);
            color: rgba(255,255,255,0.8);
        }

        .input-group .btn-outline-light:hover {
            background-color: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.5);
            color: white;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">إضافة عملية بيع جديدة</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="index.php" class="btn btn-light btn-lg">
                        <i class="fas fa-list me-2"></i>
                        قائمة المبيعات
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <form method="POST" id="saleForm">
            <div class="row">
                
                <!-- معلومات المبيعة -->
                <div class="col-lg-8">
                    <div class="form-container mb-4">
                        <h4 class="mb-4">
                            <i class="fas fa-info-circle me-2 text-primary"></i>
                            معلومات المبيعة
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="customer_id" class="form-label">العميل <span class="text-danger">*</span></label>
                                <select class="form-select" id="customer_id" name="customer_id" required>
                                    <option value="">اختر العميل</option>
                                    <?php while ($customer = $customers_result->fetch_assoc()): ?>
                                        <option value="<?php echo $customer['id']; ?>">
                                            <?php echo htmlspecialchars($customer['name']); ?>
                                            <?php if (!empty($customer['phone'])): ?>
                                                - <?php echo htmlspecialchars($customer['phone']); ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="sale_date" class="form-label">تاريخ المبيعة <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="sale_date" name="sale_date" 
                                       value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="payment_method" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="cash">نقدي</option>
                                    <option value="card">بطاقة</option>
                                    <option value="bank_transfer">تحويل بنكي</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="2" 
                                          placeholder="ملاحظات إضافية (اختياري)"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- المنتجات -->
                    <div class="form-container">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4 class="mb-0">
                                <i class="fas fa-box me-2 text-primary"></i>
                                المنتجات
                            </h4>
                            <button type="button" class="btn btn-add-product" onclick="addProductRow()">
                                <i class="fas fa-plus me-2"></i>
                                إضافة منتج
                            </button>
                        </div>
                        
                        <div class="products-table">
                            <table class="table table-bordered mb-0" id="productsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="35%">المنتج</th>
                                        <th width="15%">الكمية</th>
                                        <th width="15%">السعر</th>
                                        <th width="15%">الإجمالي</th>
                                        <th width="10%">الوحدة</th>
                                        <th width="10%">إجراء</th>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    <!-- سيتم إضافة الصفوف هنا بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- ملخص المبيعة -->
                <div class="col-lg-4">
                    <div class="summary-card">
                        <h4 class="mb-4">
                            <i class="fas fa-calculator me-2"></i>
                            ملخص المبيعة
                        </h4>
                        
                        <div class="mb-3">
                            <label class="form-label">المبلغ الإجمالي</label>
                            <input type="number" class="form-control" id="total_amount" name="total_amount" 
                                   step="0.01" readonly style="background-color: rgba(255,255,255,0.1); color: white;">
                        </div>
                        
                        <div class="mb-3">
                            <label for="discount_amount" class="form-label">مبلغ الخصم</label>
                            <input type="number" class="form-control" id="discount_amount" name="discount_amount" 
                                   step="0.01" value="0" onchange="calculateTotal()" 
                                   style="background-color: rgba(255,255,255,0.1); color: white;">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">المبلغ النهائي</label>
                            <input type="number" class="form-control" id="final_amount" name="final_amount" 
                                   step="0.01" readonly style="background-color: rgba(255,255,255,0.1); color: white;">
                        </div>
                        
                        <!-- منطقة الدفع المتقدم -->
                        <div class="payment-section mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-credit-card me-2"></i>
                                تفاصيل الدفع
                            </h6>

                            <!-- معلومات العميل والرصيد -->
                            <div id="customer-balance-info" class="alert alert-info" style="display: none;">
                                <small>
                                    <i class="fas fa-info-circle me-1"></i>
                                    <span id="customer-balance-text"></span>
                                </small>
                            </div>

                            <!-- المبلغ النقدي -->
                            <div class="mb-3">
                                <label for="cash_paid" class="form-label">المبلغ النقدي</label>
                                <input type="number" class="form-control" id="cash_paid" name="cash_paid"
                                       step="0.01" value="0" onchange="calculateAdvancedPayment()"
                                       style="background-color: rgba(255,255,255,0.1); color: white;">
                            </div>

                            <!-- استخدام الرصيد المحفوظ -->
                            <div class="mb-3" id="wallet-section" style="display: none;">
                                <label for="wallet_used" class="form-label">
                                    استخدام من الرصيد المحفوظ
                                    <span class="text-success" id="available-wallet"></span>
                                </label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="wallet_used" name="wallet_used"
                                           step="0.01" value="0" onchange="calculateAdvancedPayment()"
                                           style="background-color: rgba(255,255,255,0.1); color: white;">
                                    <button type="button" class="btn btn-outline-light" id="use-all-wallet" onclick="useAllWallet()">
                                        استخدام الكل
                                    </button>
                                </div>
                            </div>

                            <!-- إجمالي المدفوع -->
                            <div class="mb-3">
                                <label class="form-label">إجمالي المدفوع</label>
                                <input type="number" class="form-control" id="paid_amount" name="paid_amount"
                                       step="0.01" readonly style="background-color: rgba(255,255,255,0.1); color: white;">
                            </div>

                            <!-- المبلغ المتبقي -->
                            <div class="mb-3">
                                <label class="form-label">المبلغ المتبقي</label>
                                <input type="number" class="form-control" id="remaining_amount"
                                       step="0.01" readonly style="background-color: rgba(255,255,255,0.1); color: white;">
                            </div>

                            <!-- رسائل الدفع -->
                            <div id="payment-messages"></div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" name="add_sale" class="btn btn-light btn-lg">
                                <i class="fas fa-save me-2"></i>
                                حفظ المبيعة
                            </button>
                            <a href="index.php" class="btn btn-outline-light">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </div>
                
            </div>
        </form>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        // بيانات المنتجات
        const products = <?php 
            $products_result = $conn->query($products_query);
            $products_array = [];
            while ($product = $products_result->fetch_assoc()) {
                $products_array[] = $product;
            }
            echo json_encode($products_array);
        ?>;
        
        let productRowIndex = 0;
        
        $(document).ready(function() {
            // تفعيل Select2
            $('#customer_id').select2({
                placeholder: 'اختر العميل',
                allowClear: true
            });
            
            // إضافة صف منتج افتراضي
            addProductRow();
        });
        
        function addProductRow() {
            const tbody = document.getElementById('productsTableBody');
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <select class="form-select product-select" name="products[${productRowIndex}][product_id]" onchange="updateProductInfo(this, ${productRowIndex})" required>
                        <option value="">اختر المنتج</option>
                        ${products.map(product => `<option value="${product.id}" data-price="${product.selling_price}" data-stock="${product.stock_quantity}" data-unit="${product.unit}">${product.name}</option>`).join('')}
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control quantity-input" name="products[${productRowIndex}][quantity]" 
                           step="0.001" min="0.001" onchange="calculateRowTotal(${productRowIndex})" required>
                    <small class="text-muted stock-info" id="stock-${productRowIndex}"></small>
                </td>
                <td>
                    <input type="number" class="form-control price-input" name="products[${productRowIndex}][unit_price]" 
                           step="0.01" min="0.01" onchange="calculateRowTotal(${productRowIndex})" required>
                </td>
                <td>
                    <input type="number" class="form-control total-input" step="0.01" readonly>
                </td>
                <td>
                    <span class="unit-display" id="unit-${productRowIndex}">-</span>
                </td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeProductRow(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
            productRowIndex++;
        }
        
        function removeProductRow(button) {
            const row = button.closest('tr');
            row.remove();
            calculateTotal();
        }
        
        function updateProductInfo(select, index) {
            const selectedOption = select.options[select.selectedIndex];
            const price = selectedOption.getAttribute('data-price');
            const stock = selectedOption.getAttribute('data-stock');
            const unit = selectedOption.getAttribute('data-unit');
            
            const row = select.closest('tr');
            const priceInput = row.querySelector('.price-input');
            const stockInfo = document.getElementById(`stock-${index}`);
            const unitDisplay = document.getElementById(`unit-${index}`);
            
            if (price) {
                priceInput.value = price;
                stockInfo.textContent = `المتوفر: ${stock}`;
                unitDisplay.textContent = unit || '-';
                calculateRowTotal(index);
            } else {
                priceInput.value = '';
                stockInfo.textContent = '';
                unitDisplay.textContent = '-';
            }
        }
        
        function calculateRowTotal(index) {
            const row = document.querySelector(`select[name="products[${index}][product_id]"]`).closest('tr');
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const price = parseFloat(row.querySelector('.price-input').value) || 0;
            const total = quantity * price;
            
            row.querySelector('.total-input').value = total.toFixed(2);
            calculateTotal();
        }
        
        function calculateTotal() {
            let total = 0;
            document.querySelectorAll('.total-input').forEach(input => {
                total += parseFloat(input.value) || 0;
            });
            
            document.getElementById('total_amount').value = total.toFixed(2);
            
            const discount = parseFloat(document.getElementById('discount_amount').value) || 0;
            const finalAmount = total - discount;
            document.getElementById('final_amount').value = finalAmount.toFixed(2);

            // استخدام النظام المتقدم إذا كان متاحاً
            if (document.getElementById('cash_paid')) {
                calculateAdvancedPayment();
            } else {
                calculateRemaining();
            }
        }
        
        // متغيرات النظام المتقدم
        let customerWalletBalance = 0;
        let customerDebtBalance = 0;

        // تحديث معلومات العميل عند التغيير
        document.getElementById('customer_id').addEventListener('change', function() {
            updateCustomerBalance();
        });

        // تحديث رصيد العميل
        function updateCustomerBalance() {
            const customerId = document.getElementById('customer_id').value;

            if (customerId && customerId > 1) {
                // استدعاء AJAX للحصول على رصيد العميل
                fetch('../../includes/get-customer-balance.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({customer_id: customerId})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        customerWalletBalance = parseFloat(data.wallet_balance) || 0;
                        customerDebtBalance = parseFloat(data.debt_balance) || 0;

                        updateCustomerBalanceDisplay();
                        showWalletSection();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            } else {
                hideWalletSection();
            }
        }

        // عرض معلومات رصيد العميل
        function updateCustomerBalanceDisplay() {
            const balanceInfo = document.getElementById('customer-balance-info');
            const balanceText = document.getElementById('customer-balance-text');

            if (customerWalletBalance > 0 || customerDebtBalance > 0) {
                let text = '';
                if (customerWalletBalance > 0) {
                    text += `رصيد محفوظ: ${customerWalletBalance.toFixed(2)} جنيه`;
                }
                if (customerDebtBalance > 0) {
                    if (text) text += ' | ';
                    text += `مبلغ مستحق: ${customerDebtBalance.toFixed(2)} جنيه`;
                }

                balanceText.textContent = text;
                balanceInfo.style.display = 'block';
            } else {
                balanceInfo.style.display = 'none';
            }
        }

        // إظهار قسم الرصيد المحفوظ
        function showWalletSection() {
            if (customerWalletBalance > 0) {
                const walletSection = document.getElementById('wallet-section');
                const availableWallet = document.getElementById('available-wallet');

                availableWallet.textContent = `(متاح: ${customerWalletBalance.toFixed(2)} جنيه)`;
                walletSection.style.display = 'block';

                // تحديد الحد الأقصى للرصيد المستخدم
                document.getElementById('wallet_used').max = customerWalletBalance;
            }
        }

        // إخفاء قسم الرصيد المحفوظ
        function hideWalletSection() {
            document.getElementById('wallet-section').style.display = 'none';
            document.getElementById('wallet_used').value = 0;
            document.getElementById('customer-balance-info').style.display = 'none';
        }

        // استخدام كامل الرصيد المحفوظ
        function useAllWallet() {
            const finalAmount = parseFloat(document.getElementById('final_amount').value) || 0;
            const cashPaid = parseFloat(document.getElementById('cash_paid').value) || 0;
            const remaining = finalAmount - cashPaid;

            if (remaining > 0) {
                const walletToUse = Math.min(customerWalletBalance, remaining);
                document.getElementById('wallet_used').value = walletToUse.toFixed(2);
                calculateAdvancedPayment();
            }
        }

        // حساب الدفع المتقدم
        function calculateAdvancedPayment() {
            const finalAmount = parseFloat(document.getElementById('final_amount').value) || 0;
            const cashPaid = parseFloat(document.getElementById('cash_paid').value) || 0;
            const walletUsed = parseFloat(document.getElementById('wallet_used').value) || 0;

            // التحقق من صحة المبالغ
            if (walletUsed > customerWalletBalance) {
                document.getElementById('wallet_used').value = customerWalletBalance.toFixed(2);
                return calculateAdvancedPayment();
            }

            const totalPaid = cashPaid + walletUsed;
            const remaining = finalAmount - totalPaid;

            // تحديث الحقول
            document.getElementById('paid_amount').value = totalPaid.toFixed(2);
            document.getElementById('remaining_amount').value = remaining.toFixed(2);

            // عرض رسائل الدفع
            updatePaymentMessages(finalAmount, cashPaid, walletUsed, totalPaid, remaining);
        }

        // تحديث رسائل الدفع
        function updatePaymentMessages(finalAmount, cashPaid, walletUsed, totalPaid, remaining) {
            const messagesDiv = document.getElementById('payment-messages');
            let messages = '';

            if (remaining > 0) {
                messages += `<div class="alert alert-warning alert-sm">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    سيتم تسجيل مبلغ ${remaining.toFixed(2)} جنيه كدين على العميل
                </div>`;
            } else if (remaining < 0) {
                const change = Math.abs(remaining);
                messages += `<div class="alert alert-success alert-sm">
                    <i class="fas fa-plus-circle me-1"></i>
                    سيتم إضافة ${change.toFixed(2)} جنيه للرصيد المحفوظ للعميل
                </div>`;
            } else {
                messages += `<div class="alert alert-success alert-sm">
                    <i class="fas fa-check-circle me-1"></i>
                    تم دفع المبلغ بالكامل
                </div>`;
            }

            messagesDiv.innerHTML = messages;
        }

        function calculateRemaining() {
            // استخدام الدالة المتقدمة بدلاً من البسيطة
            calculateAdvancedPayment();
        }
        
        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث معلومات العميل إذا كان محدد مسبقاً
            updateCustomerBalance();

            // إضافة صف منتج افتراضي
            addProductRow();
        });

        // التحقق من صحة النموذج قبل الإرسال
        document.getElementById('saleForm').addEventListener('submit', function(e) {
            const products = document.querySelectorAll('select[name*="[product_id]"]');
            let hasProducts = false;
            
            products.forEach(select => {
                if (select.value) {
                    hasProducts = true;
                }
            });
            
            if (!hasProducts) {
                e.preventDefault();
                alert('يجب إضافة منتج واحد على الأقل');
                return false;
            }
            
            const finalAmount = parseFloat(document.getElementById('final_amount').value) || 0;
            if (finalAmount <= 0) {
                e.preventDefault();
                alert('المبلغ النهائي يجب أن يكون أكبر من صفر');
                return false;
            }
        });
    </script>

</body>
</html>
